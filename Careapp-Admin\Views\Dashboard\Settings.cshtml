@model Northwind.ViewModels.SettingsViewModel
@{
    ViewData["Title"] = "Settings";
    Layout = "Layouts/_LayoutsDash";
}

<div class="p-6">
    <!-- Header Section -->
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-800">Settings</h1>
        <p class="text-gray-600">Manage your profile and service fee settings</p>
    </div>

    <!-- Display success message if available -->
    @if (TempData["SuccessMessage"] != null)
    {
        <div class="mb-4 rounded-md bg-green-50 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-green-800">@TempData["SuccessMessage"]</p>
                </div>
            </div>
        </div>
    }

    <!-- Display error message if available -->
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="mb-4 rounded-md bg-red-50 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <p class="text-sm font-medium text-red-800">@TempData["ErrorMessage"]</p>
                </div>
            </div>
        </div>
    }

    <div class="grid grid-cols-12 gap-6">
        <!-- Settings Navigation -->
        <div class="col-span-12 lg:col-span-3">
            <div class="overflow-hidden rounded-lg bg-white shadow-sm">
                <div class="border-b border-gray-200 p-4">
                    <h2 class="text-lg font-medium text-gray-800">Settings</h2>
                </div>
                <nav class="flex flex-col p-2">
                    <a href="#profile" class="flex items-center rounded-md px-3 py-2 text-sm font-medium text-indigo-600 hover:bg-indigo-50">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                        Profile Information
                    </a>
                    <a href="#password" class="flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                        </svg>
                        Password
                    </a>
                    <a href="#service-fees" class="flex items-center rounded-md px-3 py-2 text-sm font-medium text-gray-600 hover:bg-gray-100">
                        <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Service Fee Configuration
                    </a>
                </nav>
            </div>
        </div>

        <!-- Settings Content -->
        <div class="col-span-12 space-y-6 lg:col-span-9">
            <!-- Profile Information -->
            <div id="profile" class="overflow-hidden rounded-lg bg-white shadow-sm">
                <div class="border-b border-gray-200 px-6 py-4">
                    <h2 class="text-lg font-medium text-gray-800">Profile Information</h2>
                    <p class="text-sm text-gray-600">Update your personal details and contact information</p>
                </div>
                <div class="p-6">
                    <form asp-action="UpdateProfile" asp-controller="Dashboard" method="post">
                        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2">
                            <div>
                                <label for="Name" class="block text-sm font-medium text-gray-700">Full Name</label>
                                <input type="text" id="Name" name="Name" value="@Model.Name" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            </div>
                            <div>
                                <label for="Email" class="block text-sm font-medium text-gray-700">Email Address</label>
                                <input type="email" id="Email" name="Email" value="@Model.Email" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            </div>
                            <div>
                                <label for="PhoneNumber" class="block text-sm font-medium text-gray-700">Phone Number</label>
                                <input type="text" id="PhoneNumber" name="PhoneNumber" value="@Model.PhoneNumber" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            </div>
                            <div>
                                <label for="Gender" class="block text-sm font-medium text-gray-700">Gender</label>
                                <select id="Gender" name="Gender" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                    <option value="Male" selected="@(Model.Gender == "Male")">Male</option>
                                    <option value="Female" selected="@(Model.Gender == "Female")">Female</option>
                                    <option value="Other" selected="@(Model.Gender == "Other")">Other</option>
                                    <option value="Prefer not to say" selected="@(Model.Gender == "Prefer not to say")">Prefer not to say</option>
                                </select>
                            </div>
                        </div>
                        <div class="mt-6 flex justify-end">
                            <button type="submit" class="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                Save Profile
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Password Update -->
            <div id="password" class="overflow-hidden rounded-lg bg-white shadow-sm">
                <div class="border-b border-gray-200 px-6 py-4">
                    <h2 class="text-lg font-medium text-gray-800">Password</h2>
                    <p class="text-sm text-gray-600">Update your password</p>
                </div>
                <div class="p-6">
                    <form asp-action="UpdatePassword" asp-controller="Dashboard" method="post">
                        <div class="space-y-4">
                            <div>
                                <label for="CurrentPassword" class="block text-sm font-medium text-gray-700">Current Password</label>
                                <input type="password" id="CurrentPassword" name="CurrentPassword" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            </div>
                            <div>
                                <label for="NewPassword" class="block text-sm font-medium text-gray-700">New Password</label>
                                <input type="password" id="NewPassword" name="NewPassword" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            </div>
                            <div>
                                <label for="ConfirmPassword" class="block text-sm font-medium text-gray-700">Confirm New Password</label>
                                <input type="password" id="ConfirmPassword" name="ConfirmPassword" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                            </div>
                        </div>
                        <div class="mt-6 flex justify-end">
                            <button type="submit" class="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                Update Password
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Service Fee Configuration -->
            <div id="service-fees" class="overflow-hidden rounded-lg bg-white shadow-sm">
                <div class="border-b border-gray-200 px-6 py-4">
                    <h2 class="text-lg font-medium text-gray-800">Service Fee Configuration</h2>
                    <p class="text-sm text-gray-600">Configure platform and service fees</p>
                </div>
                <div class="p-6">
                    <form asp-action="UpdateServiceFees" asp-controller="Dashboard" method="post">
                        <div class="space-y-6">
                            @if (Model.ServiceCategoryFees != null && Model.ServiceCategoryFees.Any())
                            {
                                <div>
                                    <div class="mt-4 space-y-4">
                                        @for (int i = 0; i < Model.ServiceCategoryFees.Count; i++)
                                        {
                                            <div class="flex items-center justify-between border-b border-gray-200 pb-4">
                                                <div>
                                                    <input type="hidden" name="ServiceCategoryFees[@i].CategoryId" value="@Model.ServiceCategoryFees[i].CategoryId" />
                                                    <input type="hidden" name="ServiceCategoryFees[@i].CategoryName" value="@Model.ServiceCategoryFees[i].CategoryName" />
                                                    <p class="font-medium">@Model.ServiceCategoryFees[i].CategoryName</p>
                                                </div>
                                                <div class="flex items-center">
                                                    <div class="relative rounded-md shadow-sm w-32">
                                                        <input type="number" step="0.01" min="0" max="100" id="FeePercentage_@i" name="ServiceCategoryFees[@i].FeePercentage" value="@Model.ServiceCategoryFees[i].FeePercentage" class="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm">
                                                        <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                                            <span class="text-gray-500 sm:text-sm">%</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        }
                                    </div>
                                </div>
                            }
                        </div>
                        <div class="mt-6 flex justify-end">
                            <button type="submit" class="inline-flex items-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2">
                                Save Fee Configuration
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
