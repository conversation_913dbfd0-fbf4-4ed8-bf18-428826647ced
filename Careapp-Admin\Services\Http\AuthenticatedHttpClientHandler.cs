using System.Net.Http.Headers;
using Northwind.Services.Auth;

namespace Northwind.Services.Http
{
    public class AuthenticatedHttpClientHandler : DelegatingHandler
    {
        private readonly IJwtAuthService _authService;

        public AuthenticatedHttpClientHandler(IJwtAuthService authService)
        {
            _authService = authService;
        }

        protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request, CancellationToken cancellationToken)
        {
            // Get the JWT token
            var token = _authService.GetToken();

            // If we have a token, add it to the request header
            if (!string.IsNullOrEmpty(token))
            {
                request.Headers.Authorization = new AuthenticationHeaderValue("Bearer", token);
            }

            // Continue with the request
            return await base.SendAsync(request, cancellationToken);
        }
    }
}
