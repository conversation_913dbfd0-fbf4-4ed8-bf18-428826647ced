using System.Net.Http.Headers;
using System.Text;
using System.Text.Json;

namespace Northwind.Services.API;

/// <summary>
/// Implementation of the IApiClient interface
/// </summary>
public class ApiClient : IApiClient
{
    private readonly HttpClient _httpClient;
    private readonly JsonSerializerOptions _jsonOptions;

    public ApiClient(HttpClient httpClient, IConfiguration configuration)
    {
        _httpClient = httpClient;

        // Configure the base address from configuration
        var baseUrl =
            configuration["ApiSettings:BaseUrl"]
            ?? throw new ArgumentNullException("ApiSettings:BaseUrl is not configured");
        _httpClient.BaseAddress = new Uri(baseUrl);

        // Configure default headers
        _httpClient.DefaultRequestHeaders.Accept.Clear();
        _httpClient.DefaultRequestHeaders.Accept.Add(
            new MediaTypeWithQualityHeaderValue("application/json")
        );

        // Configure JSON options
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        };
    }

    /// <inheritdoc />
    public async Task<T> GetAsync<T>(string endpoint, Dictionary<string, string>? parameters = null)
    {
        // Build query string if parameters are provided
        var queryString = string.Empty;
        if (parameters != null && parameters.Count > 0)
        {
            queryString =
                "?"
                + string.Join(
                    "&",
                    parameters.Select(p => $"{p.Key}={Uri.EscapeDataString(p.Value)}")
                );
        }

        // Send request
        var response = await _httpClient.GetAsync($"{endpoint}{queryString}");

        // Ensure success
        response.EnsureSuccessStatusCode();

        // Read and deserialize response
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<T>(content, _jsonOptions)
            ?? throw new JsonException("Failed to deserialize response");
    }

    /// <inheritdoc />
    public async Task<T> PostAsync<T, TRequest>(string endpoint, TRequest data)
    {
        // Serialize request body
        var content = new StringContent(
            JsonSerializer.Serialize(data, _jsonOptions),
            Encoding.UTF8,
            "application/json"
        );

        // Send request
        var response = await _httpClient.PostAsync(endpoint, content);

        // Ensure success
        response.EnsureSuccessStatusCode();

        // Read and deserialize response
        var responseContent = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<T>(responseContent, _jsonOptions)
            ?? throw new JsonException("Failed to deserialize response");
    }

    /// <inheritdoc />
    public async Task<T> PutAsync<T, TRequest>(string endpoint, TRequest data)
    {
        // Serialize request body
        var content = new StringContent(
            JsonSerializer.Serialize(data, _jsonOptions),
            Encoding.UTF8,
            "application/json"
        );

        // Send request
        var response = await _httpClient.PutAsync(endpoint, content);

        // Ensure success
        response.EnsureSuccessStatusCode();

        // Read and deserialize response
        var responseContent = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<T>(responseContent, _jsonOptions)
            ?? throw new JsonException("Failed to deserialize response");
    }

    /// <inheritdoc />
    public async Task<T> DeleteAsync<T>(string endpoint)
    {
        // Send request
        var response = await _httpClient.DeleteAsync(endpoint);

        // Ensure success
        response.EnsureSuccessStatusCode();

        // Read and deserialize response
        var content = await response.Content.ReadAsStringAsync();
        return JsonSerializer.Deserialize<T>(content, _jsonOptions)
            ?? throw new JsonException("Failed to deserialize response");
    }
}
