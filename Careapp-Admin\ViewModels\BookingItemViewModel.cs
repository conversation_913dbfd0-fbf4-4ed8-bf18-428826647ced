namespace Careapp_Admin.ViewModels;

public class BookingItemViewModel
{
    public string ClientName { get; set; }
    public string BookingId { get; set; }
    public string PhoneNumber { get; set; }
    public string Email { get; set; }
    public string Location { get; set; }
    public string ServiceType { get; set; }
    public DateTime BookingDate { get; set; }
    public string Duration { get; set; }
    public int Status { get; set; }

    // Helper to get status string
    public string GetStatusString()
    {
        return Status switch
        {
            0 => "Pending",
            1 => "Accepted",
            2 => "Confirmed",
            3 => "Cancelled",
            4 => "Completed",
            5 => "Rescheduled",
            _ => "Unknown",
        };
    }

    // Helper to get status CSS class
    public string GetStatusClass()
    {
        return Status switch
        {
            0 => "bg-yellow-100 text-yellow-800", // Pending
            1 => "bg-blue-100 text-blue-800", // Accepted
            2 => "bg-green-100 text-green-800", // Confirmed / Active
            3 => "bg-red-100 text-red-800", // Cancelled
            4 => "bg-gray-100 text-gray-800", // Completed
            5 => "bg-orange-100 text-orange-800", // Rescheduled
            _ =>
                "bg-gray-200 text-gray-700" // Unknown
            ,
        };
    }
}
