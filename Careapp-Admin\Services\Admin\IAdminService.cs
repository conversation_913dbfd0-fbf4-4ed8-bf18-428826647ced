using System.Threading.Tasks;
using Northwind.Models.Admin;

namespace Northwind.Services.Admin
{
    public interface IAdminService
    {
        Task<ApprovalRequestResponse> GetApprovalRequestsAsync(int page = 1, int pageSize = 10, string approvalType = null);
        Task<ApprovalActionResponse> ApproveRequestAsync(string requestId, string notes = null);
        Task<ApprovalActionResponse> RejectRequestAsync(string requestId, string rejectionReason = null, string notes = null);
    }
}
