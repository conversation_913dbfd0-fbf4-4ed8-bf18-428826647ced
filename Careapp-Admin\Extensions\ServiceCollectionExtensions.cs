using Northwind.Configuration;
using Northwind.Services.Admin;
using Northwind.Services.Api;
using Northwind.Services.Auth;
using Northwind.Services.Clients;
using Northwind.Services.Http;
using Northwind.Services.Professionals;
using Northwind.Services.Services;

namespace Northwind.Extensions
{
    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection ConfigureJwtAuthentication(
            this IServiceCollection services,
            IConfiguration configuration
        )
        {
            // Register configuration
            services.Configure<JwtSettings>(configuration.GetSection("JwtSettings"));
            services.Configure<ApiSettings>(configuration.GetSection("ApiSettings"));

            // Register HTTP context accessor
            services.AddHttpContextAccessor();

            // Register JWT authentication service
            services.AddScoped<IJwtAuthService, JwtAuthService>();

            return services;
        }

        public static IServiceCollection ConfigureApiServices(
            this IServiceCollection services,
            IConfiguration configuration
        )
        {
            var apiSettings = configuration.GetSection("ApiSettings").Get<ApiSettings>();

            // Register HTTP client factory
            services
                .AddHttpClient(
                    "ApiClient",
                    client =>
                    {
                        client.BaseAddress = new Uri(apiSettings.BaseUrl);
                        client.DefaultRequestHeaders.Add("Accept", "application/json");
                    }
                )
                .AddHttpMessageHandler<AuthenticatedHttpClientHandler>();

            // Register authenticated HTTP client handler
            services.AddTransient<AuthenticatedHttpClientHandler>();

            // Register API service
            services.AddScoped<IApiService, ApiService>();

            // Register Admin service
            services.AddScoped<IAdminService, AdminService>();

            // Register Services service
            services.AddScoped<IServicesService, ServicesService>();

            // Register Professionals service
            services.AddScoped<IProfessionalsService, ProfessionalsService>();

            // Register Clients service
            services.AddScoped<IClientsService, ClientsService>();

            return services;
        }
    }
}
