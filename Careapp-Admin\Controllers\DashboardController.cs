﻿using System.Text.Json;
using Careapp_Admin.ViewModels;
using Microsoft.AspNetCore.Mvc;
using Northwind.Models.Admin;
using Northwind.Models.Professionals;
using Northwind.Models.Services;
using Northwind.Services.Admin;
using Northwind.Services.Clients;
using Northwind.Services.Professionals;
using Northwind.Services.Services;
using Northwind.ViewModels;

namespace Careapp_Admin.Controllers;

public class DashboardController : Controller
{
    private readonly IAdminService _adminService;
    private readonly IServicesService _servicesService;
    private readonly IProfessionalsService _professionalsService;
    private readonly IClientsService _clientsService;
    private readonly ILogger<DashboardController> _logger;

    public DashboardController(
        IAdminService adminService,
        IServicesService servicesService,
        IProfessionalsService professionalsService,
        IClientsService clientsService,
        ILogger<DashboardController> logger
    )
    {
        _adminService = adminService;
        _servicesService = servicesService;
        _professionalsService = professionalsService;
        _clientsService = clientsService;
        _logger = logger;
    }

    private const int PageSize = 5;

    public IActionResult Index()
    {
        return View();
    }

    [HttpGet]
    public async Task<IActionResult> Bookings(
        string searchTerm,
        string sortBy = "bookingDate",
        bool sortDescending = true,
        int page = 1,
        string notificationType = null,
        string notificationMessage = null
    )
    {
        try
        {
            _logger.LogInformation("=== BOOKINGS ACTION START ===");
            _logger.LogInformation(
                $"Parameters: searchTerm='{searchTerm}', sortBy='{sortBy}', sortDescending={sortDescending}, page={page}"
            );

            if (
                !string.IsNullOrEmpty(notificationType)
                && !string.IsNullOrEmpty(notificationMessage)
            )
            {
                // Use a more generic TempData key or specific ones like "SuccessMessage", "ErrorMessage"
                TempData[notificationType == "success" ? "SuccessMessage" : "ErrorMessage"] =
                    notificationMessage;
            }

            _logger.LogInformation("Getting sample bookings...");
            var allBookings = GetSampleBookings(); // Replace with actual data source
            _logger.LogInformation($"Retrieved {allBookings?.Count ?? 0} bookings");

            // Ensure we have a valid list
            if (allBookings == null)
            {
                allBookings = new List<BookingItemViewModel>();
            }

            // Apply Search
            if (!string.IsNullOrEmpty(searchTerm))
            {
                allBookings = allBookings
                    .Where(b =>
                        (
                            b.ClientName?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                            ?? false
                        )
                        || (
                            b.Email?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                            ?? false
                        )
                        || (
                            b.ServiceType?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                            ?? false
                        )
                        || (
                            b.Location?.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                            ?? false
                        )
                    )
                    .ToList();
            }

            // Apply Sorting
            switch (sortBy?.ToLower())
            {
                case "clientname":
                    allBookings = sortDescending
                        ? allBookings.OrderByDescending(b => b.ClientName).ToList()
                        : allBookings.OrderBy(b => b.ClientName).ToList();
                    break;
                case "servicetype":
                    allBookings = sortDescending
                        ? allBookings
                            .OrderByDescending(b => b.ServiceType)
                            .ThenByDescending(b => b.BookingDate)
                            .ToList()
                        : allBookings
                            .OrderBy(b => b.ServiceType)
                            .ThenBy(b => b.BookingDate)
                            .ToList();
                    break;
                case "status":
                    allBookings = sortDescending
                        ? allBookings
                            .OrderByDescending(b => b.Status)
                            .ThenByDescending(b => b.BookingDate)
                            .ToList()
                        : allBookings.OrderBy(b => b.Status).ThenBy(b => b.BookingDate).ToList();
                    break;
                case "bookingdate":
                default: // Default sort
                    sortBy = "bookingDate"; // Ensure sortBy reflects default
                    allBookings = sortDescending
                        ? allBookings.OrderByDescending(b => b.BookingDate).ToList()
                        : allBookings.OrderBy(b => b.BookingDate).ToList();
                    break;
            }

            // Pagination
            int pageSize = 10; // From your sample "meta"
            var paginatedBookings = allBookings.Skip((page - 1) * pageSize).Take(pageSize).ToList();

            // Calculate pagination properties
            int totalPages = (int)Math.Ceiling(allBookings.Count / (double)pageSize);
            bool hasPreviousPage = page > 1;
            bool hasNextPage = page < totalPages;

            var viewModel = new BookingsViewModel
            {
                Bookings = paginatedBookings ?? new List<BookingItemViewModel>(),
                SearchString = searchTerm,
                SortBy = sortBy,
                SortDescending = sortDescending,
                Pagination = new PaginationMeta
                {
                    CurrentPage = page,
                    PageSize = pageSize,
                    TotalCount = allBookings.Count, // Total count of filtered items
                    TotalPages = totalPages,
                    HasPreviousPage = hasPreviousPage,
                    HasNextPage = hasNextPage,
                },
                // ErrorMessage and SuccessMessage can be set here too if needed, or use TempData
            };

            _logger.LogInformation("=== BOOKINGS ACTION SUCCESS ===");
            _logger.LogInformation(
                $"Returning view with {viewModel.Bookings?.Count ?? 0} bookings"
            );
            _logger.LogInformation(
                $"ViewModel null check: Model={viewModel != null}, Pagination={viewModel?.Pagination != null}"
            );

            await Task.Yield();

            return View(viewModel);
        }
        catch (Exception ex)
        {
            // Log the exception
            _logger.LogError(
                ex,
                "An error occurred while retrieving bookings: {Message}",
                ex.Message
            );

            // Set error message in TempData
            TempData["ErrorMessage"] = $"An error occurred while retrieving bookings: {ex.Message}";

            // Return a view with an error message and safe defaults
            return View(
                new BookingsViewModel
                {
                    ErrorMessage = $"An error occurred while retrieving bookings: {ex.Message}",
                    SearchString = searchTerm,
                    SortBy = sortBy ?? "bookingDate",
                    SortDescending = sortDescending,
                    Bookings = new List<BookingItemViewModel>(),
                    Pagination = new PaginationMeta
                    {
                        CurrentPage = page,
                        PageSize = 10,
                        TotalCount = 0,
                        TotalPages = 0,
                        HasPreviousPage = false,
                        HasNextPage = false,
                    },
                }
            );
        }
    }

    [HttpGet]
    public async Task<IActionResult> CareProfessionals(
        int page = 1,
        string searchTerm = "",
        string sortBy = "createdAt",
        bool sortDescending = true
    )
    {
        try
        {
            // Log request parameters for debugging
            _logger.LogInformation("=== CARE PROFESSIONALS REQUEST ===");
            _logger.LogInformation($"Page: {page}, PageSize: {PageSize}");
            _logger.LogInformation($"SearchTerm: {searchTerm}");
            _logger.LogInformation($"SortBy: {sortBy}, SortDescending: {sortDescending}");

            // Get care professionals data from the API
            var response = await _professionalsService.GetCareProvidersAsync(
                page,
                PageSize,
                searchTerm,
                sortBy,
                sortDescending
            );

            if (!response.Success)
            {
                // Check if it's an authentication error
                if (response.StatusCode == 401)
                {
                    // Redirect to login page
                    return RedirectToAction(
                        "Login",
                        "Auth",
                        new { returnUrl = Url.Action("CareProfessionals", "Dashboard") }
                    );
                }

                // If the API call was not successful, set error message in TempData
                TempData["ErrorMessage"] = response.Message;

                // Return a view with an error message
                return View(
                    new CareProfessionalsViewModel
                    {
                        ErrorMessage = response.Message,
                        SearchString = searchTerm,
                    }
                );
            }

            // Check if we have empty results with a search term
            if (response.Payload.Count == 0 && !string.IsNullOrWhiteSpace(searchTerm))
            {
                TempData["InfoMessage"] = $"No care professionals found matching '{searchTerm}'";
            }

            var viewModel = new CareProfessionalsViewModel
            {
                CareProviders = response.Payload,
                Pagination = response.Meta,
                SearchString = searchTerm,
                SortBy = sortBy,
                SortDescending = sortDescending,
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            // Log the exception
            _logger.LogError(
                ex,
                "An error occurred while retrieving care professionals: {Message}",
                ex.Message
            );
            _logger.LogError(ex, "Stack trace: {StackTrace}", ex.StackTrace);
            // Set error message in TempData
            TempData["ErrorMessage"] =
                $"An error occurred while retrieving care professionals: {ex.Message}";

            // Return a view with an error message
            return View(
                new CareProfessionalsViewModel
                {
                    ErrorMessage =
                        $"An error occurred while retrieving care professionals: {ex.Message}",
                    SearchString = searchTerm,
                    SortBy = sortBy,
                    SortDescending = sortDescending,
                }
            );
        }
    }

    [HttpGet]
    public async Task<IActionResult> Clients(
        int page = 1,
        string searchTerm = "",
        string sortBy = "createdAt",
        bool sortDescending = true
    )
    {
        try
        {
            _logger.LogInformation("=== DASHBOARD CONTROLLER - CLIENTS ACTION START ===");
            _logger.LogInformation($"Timestamp: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            _logger.LogInformation(
                $"Request Parameters - Page: {page}, PageSize: {PageSize}, Search: '{searchTerm}', SortBy: '{sortBy}', SortDescending: {sortDescending}"
            );
            _logger.LogInformation($"User Identity: {User?.Identity?.Name ?? "Anonymous"}");
            _logger.LogInformation(
                $"User Authenticated: {User?.Identity?.IsAuthenticated ?? false}"
            );

            // Log JWT token information if available
            if (Request.Cookies.ContainsKey("jwt_token"))
            {
                var token = Request.Cookies["jwt_token"];
                _logger.LogInformation($"JWT Token present: {!string.IsNullOrEmpty(token)}");
                _logger.LogInformation($"JWT Token length: {token?.Length ?? 0}");
            }
            else
            {
                _logger.LogInformation("JWT Token NOT found in cookies");
            }

            _logger.LogInformation("Calling ClientsService.GetClientsAsync...");

            // Get clients data from the API
            var response = await _clientsService.GetClientsAsync(
                page,
                PageSize,
                searchTerm,
                sortBy,
                sortDescending
            );

            _logger.LogInformation("=== DASHBOARD CONTROLLER - API RESPONSE ANALYSIS ===");
            _logger.LogInformation($"Response received: {response != null}");

            if (response != null)
            {
                _logger.LogInformation($"Response Success: {response.Success}");
                _logger.LogInformation($"Response Status Code: {response.StatusCode}");
                _logger.LogInformation($"Response Message: {response.Message}");
            }

            if (response is { Success: false })
            {
                _logger.LogInformation("=== API CALL FAILED ===");
                _logger.LogInformation($"Failure Status Code: {response.StatusCode}");
                _logger.LogInformation($"Failure Message: {response.Message}");

                // Check if it's an authentication error
                if (response.StatusCode == 401)
                {
                    _logger.LogInformation("AUTHENTICATION ERROR - Redirecting to login");
                    // Redirect to login page
                    return RedirectToAction(
                        "Login",
                        "Auth",
                        new
                        {
                            returnUrl = Url.Action(
                                "Clients",
                                "Dashboard",
                                new
                                {
                                    page,
                                    searchTerm,
                                    sortBy,
                                    sortDescending,
                                }
                            ),
                        }
                    );
                }

                // If the API call was not successful, set error message in TempData
                TempData["ErrorMessage"] = response.Message;
                _logger.LogInformation($"Setting error message in TempData: {response.Message}");

                // Return a view with an error message
                return View(
                    new ClientsViewModel
                    {
                        ErrorMessage = response.Message,
                        SearchString = searchTerm,
                        SortBy = sortBy,
                        SortDescending = sortDescending,
                    }
                );
            }

            _logger.LogInformation("=== API CALL SUCCESSFUL - MAPPING DATA ===");
            _logger.LogInformation($"Payload count: {response.Payload?.Count ?? 0}");

            // Map API response to view model
            var clients =
                response
                    .Payload?.Select(c => new ClientInfoVm
                    {
                        UserId = c.UserId ?? string.Empty,
                        Name = c.Name ?? string.Empty,
                        Email = c.Email ?? string.Empty,
                        PhoneNumber = c.PhoneNumber,
                        Gender = c.Gender,
                        YearsExperience = c.YearsExperience,
                        DateOfBirth = c.DateOfBirth?.ToString("yyyy-MM-dd"),
                        ProfilePictureUrl = c.ProfilePictureUrl,
                        PrimaryAddress = c.PrimaryAddress,
                        Documents = c.Documents,
                        CreatedAt = c.CreatedAt,
                        UpdatedAt = c.UpdatedAt,
                        Status = c.Status ?? "Active",
                        City = c.City,
                        Country = c.Country,
                        IsActive = c.IsActive,
                        UserType = c.UserType,
                    })
                    .ToList() ?? new List<ClientInfoVm>();

            _logger.LogInformation($"Mapped clients count: {clients.Count}");
            _logger.LogInformation("Sample client data:");
            foreach (var client in clients.Take(2))
            {
                _logger.LogInformation(
                    $"  - {client.Name} ({client.Email}) - Active: {client.IsActive}"
                );
            }

            var viewModel = new ClientsViewModel
            {
                Clients = clients,
                Pagination = response.Meta,
                SearchString = searchTerm,
                SortBy = sortBy,
                SortDescending = sortDescending,
            };

            _logger.LogInformation("=== DASHBOARD CONTROLLER - CLIENTS ACTION SUCCESS ===");
            return View(viewModel);
        }
        catch (Exception ex)
        {
            _logger.LogError("=== DASHBOARD CONTROLLER EXCEPTION ===");
            _logger.LogError($"Exception Type: {ex.GetType().Name}");
            _logger.LogError($"Exception Message: {ex.Message}");
            _logger.LogError($"Inner Exception: {ex.InnerException?.Message}");
            _logger.LogError($"Stack Trace: {ex.StackTrace}");
            _logger.LogError("=== END DASHBOARD CONTROLLER EXCEPTION ===");

            // Set error message in TempData
            TempData["ErrorMessage"] = $"An error occurred while retrieving clients: {ex.Message}";

            // Return a view with an error message
            return View(
                new ClientsViewModel
                {
                    ErrorMessage = $"An error occurred while retrieving clients: {ex.Message}",
                    SearchString = searchTerm,
                    SortBy = sortBy,
                    SortDescending = sortDescending,
                }
            );
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetProviderApprovalRequests(string userId)
    {
        try
        {
            if (string.IsNullOrEmpty(userId))
            {
                return Json(new { success = false, message = "User ID is required" });
            }

            // Log the request with detailed information
            _logger.LogInformation("=== GET PROVIDER APPROVAL REQUESTS START ===");
            _logger.LogInformation($"Timestamp: {DateTime.UtcNow:yyyy-MM-dd HH:mm:ss} UTC");
            _logger.LogInformation($"User ID: {userId}");
            _logger.LogInformation(
                $"User ID Type: {(userId == null ? "null" : userId.GetType().Name)}"
            );
            _logger.LogInformation($"User ID Length: {userId?.Length ?? 0}");
            _logger.LogInformation($"Request URL: {Request.Path}{Request.QueryString}");
            _logger.LogInformation($"Request Method: {Request.Method}");
            _logger.LogInformation("Request Headers:");
            foreach (var header in Request.Headers)
            {
                _logger.LogInformation($"  {header.Key}: {header.Value}");
            }

            // Call the API to get approval requests
            var response = await _professionalsService.GetProviderApprovalRequestsAsync(userId);

            if (!response.Success)
            {
                // Check if it's an authentication error
                if (response.StatusCode == 401)
                {
                    // Return a 401 status code for AJAX to handle
                    return Json(
                        new
                        {
                            success = false,
                            message = "Authentication failed. Please log in again.",
                            statusCode = 401,
                        }
                    );
                }

                // Log the error message
                _logger.LogError($"Error getting approval requests: {response.Message}");

                // Return an error message
                return Json(new { success = false, message = response.Message });
            }

            // Log the response details
            _logger.LogInformation("=== APPROVAL REQUESTS RESPONSE ===");
            _logger.LogInformation($"Response Success: {response.Success}");
            _logger.LogInformation($"Response Status Code: {response.StatusCode}");
            _logger.LogInformation($"Response Message: {response.Message}");

            // Initialize the pendingRequests list
            List<ApprovalRequest> pendingRequests;

            if (response.Payload != null)
            {
                _logger.LogInformation($"Total Requests: {response.Payload.Count}");
                _logger.LogInformation("All Requests:");
                foreach (var request in response.Payload)
                {
                    _logger.LogInformation(
                        $"  - ID: {request.Id}, User ID: {request.UserId}, Type: {request.ApprovalType}, Status: {request.Status}"
                    );
                }

                // Get all requests, not just pending ones
                var allRequests = response.Payload.ToList();

                // Also filter for pending requests for logging purposes
                var pendingRequest = allRequests.Where(r => r.Status == "Pending").ToList();
                var approvedRequests = allRequests.Where(r => r.Status == "Approved").ToList();
                var rejectedRequests = allRequests.Where(r => r.Status == "Rejected").ToList();

                _logger.LogInformation($"All Requests: {allRequests.Count}");
                _logger.LogInformation($"Pending Requests: {pendingRequest.Count}");
                _logger.LogInformation($"Approved Requests: {approvedRequests.Count}");
                _logger.LogInformation($"Rejected Requests: {rejectedRequests.Count}");

                _logger.LogInformation("Pending Requests Details:");
                foreach (var request in pendingRequest)
                {
                    _logger.LogInformation(
                        $"  - ID: {request.Id}, User ID: {request.UserId}, Type: {request.ApprovalType}, Created: {request.CreatedAt}"
                    );
                }

                // Log success
                _logger.LogInformation(
                    $"Found {allRequests.Count} total approval requests for provider {userId} ({pendingRequest.Count} pending, {approvedRequests.Count} approved, {rejectedRequests.Count} rejected)"
                );

                // Use all requests instead of just pending ones
                pendingRequests = allRequests;
            }
            else
            {
                _logger.LogInformation("Response Payload is NULL");
                // Create an empty list if payload is null
                pendingRequests = new List<ApprovalRequest>();
            }

            _logger.LogInformation("=== GET PROVIDER APPROVAL REQUESTS END ===");

            // Return success response with the approval requests
            return Json(new { success = true, requests = pendingRequests });
        }
        catch (Exception ex)
        {
            // Log the exception
            _logger.LogError($"Exception in GetProviderApprovalRequests: {ex.Message}");
            _logger.LogError($"Stack trace: {ex.StackTrace}");

            // Return an error message
            return Json(new { success = false, message = $"An error occurred: {ex.Message}" });
        }
    }

    [HttpGet]
    public async Task<IActionResult> GetClientProfile(string clientId)
    {
        try
        {
            _logger.LogInformation($"=== GET CLIENT PROFILE REQUEST ===");
            _logger.LogInformation($"Client ID: {clientId}");

            if (string.IsNullOrEmpty(clientId))
            {
                return BadRequest("Client ID is required");
            }

            var response = await _clientsService.GetClientProfileAsync(clientId);

            _logger.LogInformation($"Profile response success: {response.Success}");

            if (!response.Success)
            {
                if (response.StatusCode == 401)
                {
                    return StatusCode(401, "Authentication failed. Please log in again.");
                }

                return BadRequest(response.Message);
            }

            return Json(response);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Exception in GetClientProfile: {ex.Message}");
            _logger.LogError($"Stack trace: {ex.StackTrace}");
            return StatusCode(500, $"An error occurred: {ex.Message}");
        }
    }

    [HttpGet]
    public async Task<IActionResult> Settings()
    {
        // Get care categories for service fee configuration
        var careCategories = new List<CareCategory>();
        try
        {
            var response = await _servicesService.GetCareCategoriesAsync(true);
            if (response.Success)
            {
                careCategories = response.Payload;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error fetching care categories: {ex.Message}");
            // Continue with empty list if there's an error
        }

        // Get platform fee settings from in-memory storage
        var platformFeePercentage = Northwind
            .Services
            .InMemoryStorage
            .PlatformFeeStorage
            .PlatformFeePercentage;
        var serviceFeePercentage = Northwind
            .Services
            .InMemoryStorage
            .PlatformFeeStorage
            .ServiceFeePercentage;
        var minimumFeeAmount = Northwind
            .Services
            .InMemoryStorage
            .PlatformFeeStorage
            .MinimumFeeAmount;
        var maximumFeeAmount = Northwind
            .Services
            .InMemoryStorage
            .PlatformFeeStorage
            .MaximumFeeAmount;
        var applyFeesToAllServices = Northwind
            .Services
            .InMemoryStorage
            .PlatformFeeStorage
            .ApplyFeesToAllServices;

        // Create service category fees
        var serviceCategoryFees = careCategories
            .Select(c => new ServiceCategoryFee
            {
                CategoryId = c.Id,
                CategoryName = c.Name,
                FeePercentage = serviceFeePercentage, // Default to global service fee
                IsCustomFee = false,
            })
            .ToList();

        var viewModel = new SettingsViewModel
        {
            // Profile Information (mock data)
            Name = "John Doe",
            Email = "<EMAIL>",
            PhoneNumber = "(*************",
            Gender = "Male",

            // Service Fee Configuration
            PlatformFeePercentage = platformFeePercentage,
            ServiceFeePercentage = serviceFeePercentage,
            MinimumFeeAmount = minimumFeeAmount,
            MaximumFeeAmount = maximumFeeAmount,
            ApplyFeesToAllServices = applyFeesToAllServices,
            ServiceCategoryFees = serviceCategoryFees,
        };

        return View(viewModel);
    }

    [HttpPost]
    public async Task<IActionResult> ApproveRequest(string requestId, string notes)
    {
        try
        {
            if (string.IsNullOrEmpty(requestId))
            {
                return BadRequest("Request ID is required");
            }

            // Log the request ID being approved
            _logger.LogInformation($"Approving request with ID: {requestId}");
            _logger.LogInformation($"Notes: {notes}");

            // Call the API to approve the request with notes
            var response = await _adminService.ApproveRequestAsync(requestId, notes);

            if (!response.Success)
            {
                // Check if it's an authentication error
                if (response.StatusCode == 401)
                {
                    // Return a 401 status code for HTMX to handle
                    return StatusCode(401, "Authentication failed. Please log in again.");
                }

                // Log the error message
                _logger.LogError($"Error approving request: {response.Message}");

                // Return an error message
                return BadRequest(response.Message);
            }

            // Log success
            _logger.LogInformation($"Request {requestId} approved successfully");

            // Set success message in TempData
            TempData["SuccessMessage"] = $"Request {requestId} approved successfully";

            // Get the updated request to render the partial view
            var requestsResponse = await _adminService.GetApprovalRequestsAsync(1, 1, string.Empty);

            if (
                !requestsResponse.Success
                || requestsResponse.Payload == null
                || requestsResponse.Payload.Count == 0
            )
            {
                // If we can't get the updated request, return a success message
                return Content(
                    "<tr><td colspan='8' class='px-6 py-4 text-center text-sm text-green-500'>Request approved successfully</td></tr>"
                );
            }

            // Find the approved request in the response
            var approvedRequest = requestsResponse.Payload.FirstOrDefault(r => r.Id == requestId);

            if (approvedRequest == null)
            {
                // If we can't find the request, it might have been removed after approval
                return Content(
                    "<tr><td colspan='8' class='px-6 py-4 text-center text-sm text-green-500'>Request approved and processed</td></tr>"
                );
            }

            // Return the partial view with the updated request
            return PartialView("_ApprovalRequestRow", approvedRequest);
        }
        catch (Exception ex)
        {
            // Log the exception
            _logger.LogError($"Exception in ApproveRequest: {ex.Message}");
            _logger.LogError($"Stack trace: {ex.StackTrace}");

            // Return an error message
            return BadRequest($"Failed to approve request: {ex.Message}");
        }
    }

    [HttpPost]
    public async Task<IActionResult> ApproveProvider(string requestId, string notes)
    {
        try
        {
            if (string.IsNullOrEmpty(requestId))
            {
                return Json(new { success = false, message = "Request ID is required" });
            }

            // Log the request ID being approved
            _logger.LogInformation($"Approving provider request with ID: {requestId}");
            _logger.LogInformation($"Notes: {notes}");

            // Call the professionals service to approve the provider
            var response = await _professionalsService.ApproveProviderAsync(requestId, notes);

            if (!response.Success)
            {
                // Check if it's an authentication error
                if (response.StatusCode == 401)
                {
                    return Json(
                        new
                        {
                            success = false,
                            message = "Authentication failed. Please log in again.",
                            statusCode = 401,
                        }
                    );
                }

                // Log the error message
                _logger.LogError($"Error approving provider: {response.Message}");

                // Return an error message
                return Json(new { success = false, message = response.Message });
            }

            // Log success
            _logger.LogInformation($"Provider request {requestId} approved successfully");

            // Return success response
            return Json(
                new { success = true, message = "Provider has been approved successfully." }
            );
        }
        catch (Exception ex)
        {
            // Log the exception
            _logger.LogError($"Exception in ApproveProvider: {ex.Message}");
            _logger.LogError($"Stack trace: {ex.StackTrace}");

            // Return an error message
            return Json(
                new
                {
                    success = false,
                    message = $"An error occurred while approving the provider: {ex.Message}",
                }
            );
        }
    }

    [HttpPost]
    public async Task<IActionResult> RejectProvider([FromBody] RejectProviderRequest request)
    {
        try
        {
            if (request == null || string.IsNullOrEmpty(request.ProviderId))
            {
                return Json(new { success = false, message = "Provider ID is required" });
            }

            if (string.IsNullOrEmpty(request.RejectionReason))
            {
                return Json(new { success = false, message = "Rejection reason is required" });
            }

            // Log the request ID being rejected
            _logger.LogInformation($"Rejecting provider request with ID: {request.ProviderId}");
            _logger.LogInformation($"Rejection reason: {request.RejectionReason}");
            _logger.LogInformation($"Notes: {request.Notes}");

            // Call the professionals service to reject the provider
            var response = await _professionalsService.RejectProviderAsync(
                request.ProviderId,
                request.RejectionReason,
                request.Notes
            );

            if (!response.Success)
            {
                // Check if it's an authentication error
                if (response.StatusCode == 401)
                {
                    return Json(
                        new
                        {
                            success = false,
                            message = "Authentication failed. Please log in again.",
                            statusCode = 401,
                        }
                    );
                }

                // Log the error message
                _logger.LogError($"Error rejecting provider: {response.Message}");

                // Return an error message
                return Json(new { success = false, message = response.Message });
            }

            // Log success
            _logger.LogInformation($"Provider request {request.ProviderId} rejected successfully");

            // Return success response
            return Json(
                new { success = true, message = "Provider has been rejected successfully." }
            );
        }
        catch (Exception ex)
        {
            // Log the exception
            _logger.LogError($"Exception in RejectProvider: {ex.Message}");
            _logger.LogError($"Stack trace: {ex.StackTrace}");

            // Return an error message
            return Json(
                new
                {
                    success = false,
                    message = $"An error occurred while rejecting the provider: {ex.Message}",
                }
            );
        }
    }

    [HttpPost]
    public async Task<IActionResult> RejectRequest(
        string requestId,
        string rejectionReason,
        string notes
    )
    {
        try
        {
            if (string.IsNullOrEmpty(requestId))
            {
                return BadRequest("Request ID is required");
            }

            // Log the request ID being rejected
            _logger.LogInformation($"Rejecting request with ID: {requestId}");
            _logger.LogInformation($"Rejection reason: {rejectionReason}");
            _logger.LogInformation($"Notes: {notes}");

            // Call the API to reject the request
            var response = await _adminService.RejectRequestAsync(
                requestId,
                rejectionReason,
                notes
            );

            if (!response.Success)
            {
                // Check if it's an authentication error
                if (response.StatusCode == 401)
                {
                    // Return a 401 status code for HTMX to handle
                    return StatusCode(401, "Authentication failed. Please log in again.");
                }

                // Log the error message
                _logger.LogError($"Error rejecting request: {response.Message}");

                // Return an error message
                return BadRequest(response.Message);
            }

            // Log success
            _logger.LogInformation($"Request {requestId} rejected successfully");

            // Set success message in TempData
            TempData["SuccessMessage"] = $"Request {requestId} rejected successfully";

            // Get the updated request to render the partial view
            var requestsResponse = await _adminService.GetApprovalRequestsAsync(1, 1, string.Empty);

            if (
                !requestsResponse.Success
                || requestsResponse.Payload == null
                || requestsResponse.Payload.Count == 0
            )
            {
                // If we can't get the updated request, return a success message
                return Content(
                    "<tr><td colspan='8' class='px-6 py-4 text-center text-sm text-red-500'>Request rejected successfully</td></tr>"
                );
            }

            // Find the rejected request in the response
            var rejectedRequest = requestsResponse.Payload.FirstOrDefault(r => r.Id == requestId);

            if (rejectedRequest == null)
            {
                // If we can't find the request, it might have been removed after rejection
                return Content(
                    "<tr><td colspan='8' class='px-6 py-4 text-center text-sm text-red-500'>Request rejected and processed</td></tr>"
                );
            }

            // Return the partial view with the updated request
            return PartialView("_ApprovalRequestRow", rejectedRequest);
        }
        catch (Exception ex)
        {
            // Log the exception
            _logger.LogError($"Exception in RejectRequest: {ex.Message}");
            _logger.LogError($"Stack trace: {ex.StackTrace}");

            // Return an error message
            return BadRequest($"Failed to reject request: {ex.Message}");
        }
    }

    [HttpGet]
    public async Task<IActionResult> Services(bool includeInactive = true)
    {
        try
        {
            // Get care categories from the API
            var response = await _servicesService.GetCareCategoriesAsync(includeInactive);

            if (!response.Success)
            {
                // Check if it's an authentication error
                if (response.StatusCode == 401)
                {
                    // Redirect to login page
                    return RedirectToAction(
                        "Login",
                        "Auth",
                        new { returnUrl = Url.Action("Services", "Dashboard") }
                    );
                }

                // If the API call fails for other reasons, return a view with an error message
                return View(
                    new ServicesViewModel
                    {
                        ErrorMessage = response.Message ?? "Failed to retrieve care categories",
                    }
                );
            }

            // Create the view model
            var viewModel = new ServicesViewModel
            {
                CareCategories = response.Payload,
                IncludeInactive = includeInactive,
            };

            return View(viewModel);
        }
        catch (Exception ex)
        {
            // If an exception occurs, return a view with an error message
            return View(
                new ServicesViewModel { ErrorMessage = $"An error occurred: {ex.Message}" }
            );
        }
    }

    [HttpPost]
    public async Task<IActionResult> ActivateCategory(
        string categoryId,
        bool includeInactive = true
    )
    {
        try
        {
            if (string.IsNullOrEmpty(categoryId))
            {
                TempData["ErrorMessage"] = "Category ID is required";
                return RedirectToAction("Services", new { includeInactive });
            }

            // Log the category ID being activated
            _logger.LogInformation($"Activating category with ID: {categoryId}");

            // Call the API to activate the category
            var success = await _servicesService.ActivateCategoryAsync(categoryId);

            if (!success)
            {
                TempData["ErrorMessage"] =
                    "Failed to activate category. Please check the logs for more details.";
            }
            else
            {
                TempData["SuccessMessage"] = "Category activated successfully";
            }

            // Redirect back to the services page
            return RedirectToAction("Services", new { includeInactive });
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error in ActivateCategory: {ex.Message}");
            TempData["ErrorMessage"] = $"An error occurred: {ex.Message}";
            return RedirectToAction("Services", new { includeInactive });
        }
    }

    [HttpPost]
    public async Task<IActionResult> DeactivateCategory(
        string categoryId,
        bool includeInactive = true
    )
    {
        try
        {
            if (string.IsNullOrEmpty(categoryId))
            {
                TempData["ErrorMessage"] = "Category ID is required";
                return RedirectToAction("Services", new { includeInactive });
            }

            // Log the category ID being deactivated
            _logger.LogInformation($"Deactivating category with ID: {categoryId}");

            // Call the API to deactivate the category
            var success = await _servicesService.DeactivateCategoryAsync(categoryId);

            if (!success)
            {
                TempData["ErrorMessage"] =
                    "Failed to deactivate category. Please check the logs for more details.";
            }
            else
            {
                TempData["SuccessMessage"] = "Category deactivated successfully";
            }

            // Redirect back to the services page
            return RedirectToAction("Services", new { includeInactive });
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error in DeactivateCategory: {ex.Message}");
            TempData["ErrorMessage"] = $"An error occurred: {ex.Message}";
            return RedirectToAction("Services", new { includeInactive });
        }
    }

    [HttpPost]
    public async Task<IActionResult> AddCategory(
        string name,
        string description,
        bool includeInactive = true
    )
    {
        try
        {
            if (string.IsNullOrEmpty(name))
            {
                TempData["ErrorMessage"] = "Category name is required";
                return RedirectToAction("Services", new { includeInactive });
            }

            // Log the category being added
            _logger.LogInformation($"Adding new category: {name}");

            // Call the API to add the category
            var success = await _servicesService.AddCategoryAsync(name, description);

            if (!success)
            {
                TempData["ErrorMessage"] =
                    "Failed to add category. Please check the logs for more details.";
            }
            else
            {
                TempData["SuccessMessage"] = "Category added successfully";
            }

            // Redirect back to the services page
            return RedirectToAction("Services", new { includeInactive });
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error in AddCategory: {ex.Message}");
            TempData["ErrorMessage"] = $"An error occurred: {ex.Message}";
            return RedirectToAction("Services", new { includeInactive });
        }
    }

    [HttpPost]
    public async Task<IActionResult> DeleteCategory(string categoryId, bool includeInactive = true)
    {
        try
        {
            if (string.IsNullOrEmpty(categoryId))
            {
                TempData["ErrorMessage"] = "Category ID is required";
                return RedirectToAction("Services", new { includeInactive });
            }

            // Log the category ID being deleted
            _logger.LogInformation($"Deleting category with ID: {categoryId}");

            // Call the API to delete the category
            var success = await _servicesService.DeleteCategoryAsync(categoryId);

            if (!success)
            {
                TempData["ErrorMessage"] =
                    "Failed to delete category. Please check the logs for more details.";
            }
            else
            {
                TempData["SuccessMessage"] = "Category deleted successfully";
            }

            // Redirect back to the services page
            return RedirectToAction("Services", new { includeInactive });
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error in DeleteCategory: {ex.Message}");
            TempData["ErrorMessage"] = $"An error occurred: {ex.Message}";
            return RedirectToAction("Services", new { includeInactive });
        }
    }

    [HttpPost]
    public async Task<IActionResult> UpdateCategory(
        string categoryId,
        string name,
        string description,
        bool isActive,
        bool includeInactive = true
    )
    {
        var success = await _servicesService.UpdateCategoryAsync(
            categoryId,
            name,
            description,
            isActive
        );
        if (success)
        {
            TempData["SuccessMessage"] = "Category updated successfully";
        }
        else
        {
            TempData["ErrorMessage"] = "Failed to update category";
        }

        return RedirectToAction("Services", new { includeInactive });
    }

    private List<BookingItemViewModel> GetSampleBookings()
    {
        try
        {
            string jsonData =
                @"
            [
                { ""clientName"": ""Olivia Martinez"", ""bookingId"": ""098cac75-3a96-4346-a5d7-76e9d785bc5e"", ""phoneNumber"": ""+1987654328"", ""email"": ""<EMAIL>"", ""location"": ""753 Willow Way, San Diego, CA, 92101"", ""serviceType"": ""Child Care"", ""bookingDate"": ""2025-06-26T00:00:00Z"", ""duration"": ""4h 0m"", ""status"": 2 },
                { ""clientName"": ""Jane Smith"", ""bookingId"": ""2390c5ae-79b0-4982-a369-7ad924616652"", ""phoneNumber"": ""+1987654322"", ""email"": ""<EMAIL>"", ""location"": ""456 Oak Ave, Los Angeles, CA, 90001"", ""serviceType"": ""Elderly Care"", ""bookingDate"": ""2025-06-11T00:00:00Z"", ""duration"": ""3h 0m"", ""status"": 2 },
                { ""clientName"": ""John Doe"", ""bookingId"": ""23c5cd72-82b6-435a-923d-7b61b72aa181"", ""phoneNumber"": ""+1987654321"", ""email"": ""<EMAIL>"", ""location"": ""123 Main St, New York, NY, 10001"", ""serviceType"": ""Disability Care"", ""bookingDate"": ""2025-06-13T00:00:00Z"", ""duration"": ""4h 0m"", ""status"": 0 },
                { ""clientName"": ""William Brown"", ""bookingId"": ""2647c6b1-f4c9-4e58-a198-d6319b9ab513"", ""phoneNumber"": ""+1987654327"", ""email"": ""<EMAIL>"", ""location"": ""159 Elm St, San Antonio, TX, 78201"", ""serviceType"": ""Elderly Care"", ""bookingDate"": ""2025-07-01T00:00:00Z"", ""duration"": ""1h 0m"", ""status"": 0 },
                { ""clientName"": ""Noah Anderson"", ""bookingId"": ""2d95ff3a-343a-49ed-8dd3-f162c6c84444"", ""phoneNumber"": ""+1987654329"", ""email"": ""<EMAIL>"", ""location"": ""852 Spruce St, Dallas, TX, 75201"", ""serviceType"": ""Child Care"", ""bookingDate"": ""2025-06-23T00:00:00Z"", ""duration"": ""3h 0m"", ""status"": 4 },
                { ""clientName"": ""Maria Garcia"", ""bookingId"": ""4d721cff-e584-4a87-9052-b4d0498070fc"", ""phoneNumber"": ""+1987654324"", ""email"": ""<EMAIL>"", ""location"": ""321 Maple Dr, Houston, TX, 77001"", ""serviceType"": ""Elderly Care"", ""bookingDate"": ""2025-06-27T00:00:00Z"", ""duration"": ""1h 0m"", ""status"": 4 },
                { ""clientName"": ""Sophia Thomas"", ""bookingId"": ""5e4067a2-9b18-4083-a7f5-b2186d989d36"", ""phoneNumber"": ""+1987654330"", ""email"": ""<EMAIL>"", ""location"": ""426 Redwood Rd, San Jose, CA, 95101"", ""serviceType"": ""Disability Care"", ""bookingDate"": ""2025-06-05T00:00:00Z"", ""duration"": ""1h 0m"", ""status"": 3 },
                { ""clientName"": ""Olivia Martinez"", ""bookingId"": ""6af8dc2f-6385-4034-8fd9-900f319a471a"", ""phoneNumber"": ""+1987654328"", ""email"": ""<EMAIL>"", ""location"": ""753 Willow Way, San Diego, CA, 92101"", ""serviceType"": ""Disability Care"", ""bookingDate"": ""2025-07-03T00:00:00Z"", ""duration"": ""1h 0m"", ""status"": 2 },
                { ""clientName"": ""James Wilson"", ""bookingId"": ""80dae2df-40ae-4c8f-b871-23866f5a7b8e"", ""phoneNumber"": ""+1987654325"", ""email"": ""<EMAIL>"", ""location"": ""654 Cedar Ln, Phoenix, AZ, 85001"", ""serviceType"": ""Disability Care"", ""bookingDate"": ""2025-06-07T00:00:00Z"", ""duration"": ""2h 0m"", ""status"": 0 },
                { ""clientName"": ""Alex Johnson"", ""bookingId"": ""871a53cb-a144-4b1c-ab2a-5d1e228616f5"", ""phoneNumber"": ""+1987654323"", ""email"": ""<EMAIL>"", ""location"": ""789 Pine Rd, Chicago, IL, 60601"", ""serviceType"": ""Child Care"", ""bookingDate"": ""2025-06-15T00:00:00Z"", ""duration"": ""3h 0m"", ""status"": 2 }
            ]";

            var options = new JsonSerializerOptions { PropertyNameCaseInsensitive = true };
            var bookings = JsonSerializer.Deserialize<List<BookingItemViewModel>>(
                jsonData,
                options
            );
            return bookings ?? new List<BookingItemViewModel>();
        }
        catch (Exception ex)
        {
            // Log the error and return empty list
            _logger.LogError(ex, "Error deserializing sample bookings data: {Message}", ex.Message);
            return new List<BookingItemViewModel>();
        }
    }
}
