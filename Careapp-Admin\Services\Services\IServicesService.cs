using System.Threading.Tasks;
using Northwind.Models.Services;

namespace Northwind.Services.Services
{
    public interface IServicesService
    {
        Task<CareCategoryResponse> GetCareCategoriesAsync(bool includeInactive = true);
        Task<bool> ActivateCategoryAsync(string categoryId);
        Task<bool> DeactivateCategoryAsync(string categoryId);
        Task<bool> AddCategoryAsync(string name, string description);
        Task<bool> UpdateCategoryAsync(
            string categoryId,
            string name,
            string description,
            bool isActive
        );
        Task<bool> DeleteCategoryAsync(string categoryId);
    }
}
