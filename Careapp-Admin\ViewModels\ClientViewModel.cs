using System.Collections.Generic;
using Northwind.Models.Admin;

namespace Northwind.ViewModels
{
    public class ClientInfoVm
    {
        public string UserId { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string? PhoneNumber { get; set; }
        public string? Gender { get; set; }
        public int YearsExperience { get; set; } = 0;
        public string? DateOfBirth { get; set; } // Using string for display formatting
        public string? ProfilePictureUrl { get; set; }
        public string? PrimaryAddress { get; set; }
        public string? Documents { get; set; }
        public DateTime? CreatedAt { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string Status { get; set; } = "Active";
        public string? City { get; set; }
        public string? Country { get; set; }
        public bool IsActive { get; set; } = true;
        public string? UserType { get; set; }
    }

    public class ClientsViewModel
    {
        public List<ClientInfoVm> Clients { get; set; }
        public PaginationMeta Pagination { get; set; }
        public string SearchString { get; set; }
        public string ErrorMessage { get; set; }
        public string SuccessMessage { get; set; }
        public string SortBy { get; set; } = "createdAt";
        public bool SortDescending { get; set; } = true;

        public ClientsViewModel()
        {
            Clients = new List<ClientInfoVm>();
            Pagination = new PaginationMeta();
        }
    }
}
