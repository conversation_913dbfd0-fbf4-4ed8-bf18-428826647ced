using System;
using System.Text.Json.Serialization;

namespace Northwind.Models.Admin
{
    public class ApprovalActionResponse
    {
        [JsonPropertyName("apiResponseId")]
        public string ApiResponseId { get; set; }
        
        [JsonPropertyName("success")]
        public bool Success { get; set; }
        
        [JsonPropertyName("statusCode")]
        public int StatusCode { get; set; }
        
        [JsonPropertyName("message")]
        public string Message { get; set; }
        
        [JsonPropertyName("payload")]
        public ApprovalActionPayload Payload { get; set; }
        
        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; }
    }
    
    public class ApprovalActionPayload
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }
        
        [JsonPropertyName("status")]
        public string Status { get; set; }
        
        [JsonPropertyName("processedAt")]
        public DateTime ProcessedAt { get; set; }
        
        [JsonPropertyName("processedBy")]
        public string ProcessedBy { get; set; }
        
        [Json<PERSON>ropertyName("processedByName")]
        public string ProcessedByName { get; set; }
    }
}
