using Northwind.Models.Auth;

namespace Northwind.Services.Auth
{
    public interface IJwtAuthService
    {
        Task<AuthResponse> LoginAsync(string email, string password);
        Task<AuthResponse> RefreshTokenAsync(string refreshToken);
        Task<bool> LogoutAsync();
        bool IsAuthenticated();
        string GetToken();
        string GetRefreshToken();
        DateTime? GetTokenExpiryTime();
        bool ShouldRefreshToken();
        void StoreTokens(string token, string refreshToken, DateTime expiresIn);
        void ClearTokens();
    }
}
