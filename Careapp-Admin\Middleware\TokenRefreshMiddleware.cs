using Northwind.Services.Auth;

namespace Northwind.Middleware
{
    public class TokenRefreshMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<TokenRefreshMiddleware> _logger;
        private readonly string[] _excludedPaths = new[]
        {
            "/auth/login",
            "/auth/register",
            "/auth/forgotpassword",
            "/auth/otpverification",
            "/auth/resetpassword",
        };

        public TokenRefreshMiddleware(RequestDelegate next, ILogger<TokenRefreshMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IJwtAuthService authService)
        {
            var path = context.Request.Path.Value?.ToLower();

            // Skip token refresh for excluded paths
            if (path != null && IsExcludedPath(path))
            {
                await _next(context);
                return;
            }

            // Check if user is authenticated
            if (authService.IsAuthenticated())
            {
                // Check if token needs to be refreshed
                if (authService.ShouldRefreshToken())
                {
                    _logger.LogInformation("Token is about to expire. Attempting to refresh...");

                    try
                    {
                        var refreshToken = authService.GetRefreshToken();
                        if (!string.IsNullOrEmpty(refreshToken))
                        {
                            var refreshResult = await authService.RefreshTokenAsync(refreshToken);

                            if (refreshResult.Success)
                            {
                                _logger.LogInformation("Token refreshed successfully.");
                            }
                            else
                            {
                                _logger.LogWarning(
                                    "Failed to refresh token: {Message}",
                                    refreshResult.Message
                                );
                            }
                        }
                        else
                        {
                            _logger.LogWarning("No refresh token available.");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error refreshing token");
                    }
                }
            }

            await _next(context);
        }

        private bool IsExcludedPath(string path)
        {
            foreach (var excludedPath in _excludedPaths)
            {
                if (path.StartsWith(excludedPath))
                {
                    return true;
                }
            }

            // Also exclude static files
            if (
                path.StartsWith("/css/")
                || path.StartsWith("/js/")
                || path.StartsWith("/lib/")
                || path.StartsWith("/images/")
                || path.StartsWith("/favicon.ico")
            )
            {
                return true;
            }

            return false;
        }
    }
}
