@model Northwind.ViewModels.ForgotPasswordVm
@{
    ViewData["Title"] = "Forgot Password";
    Layout = "Layouts/_LayoutAuth";
}

<partial name="_SuccessNotification" />
<partial name="_ErrorNotification" />

<div class="font-[sans-serif]">
    <div class="flex flex-col items-center justify-center min-h-screen px-4">
        <div class="w-full max-w-md">
            <a href="@Url.Action("Index", "Dashboard")">
                <img src="~/images/logo-new.svg" alt="logo" class="mx-auto mb-6 block w-40"
                    style="filter: brightness(0) invert(0.1) sepia(1) saturate(5) hue-rotate(200deg);" />
            </a>

            <div class="rounded-2xl bg-white p-8 shadow">
                <h2 class="text-center text-2xl font-bold" style="color: #171e54;">Forgot Password</h2>
                <p class="text-center text-sm text-gray-600 mt-2">Enter your email to receive a verification code</p>

                <form asp-controller="Auth" asp-action="ForgotPassword" method="post" class="mt-8 space-y-6">
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <div class="mt-1">
                            <input asp-for="Email" type="email"
                                class="w-full rounded-md border border-gray-300 px-4 py-3 text-sm text-gray-800"
                                style="outline-color: #171e54;" placeholder="Enter your email" />
                            <span asp-validation-for="Email" class="text-xs text-red-500"></span>
                        </div>
                    </div>

                    <div>
                        <button type="submit"
                            class="w-full rounded-lg px-4 py-3 text-sm tracking-wide text-white focus:outline-none"
                            style="background-color: #171e54; transition: background-color 0.3s ease;"
                            onmouseover="this.style.backgroundColor='#0f1436'"
                            onmouseout="this.style.backgroundColor='#171e54'">
                            Send Verification Code
                        </button>
                    </div>

                    <div class="text-center">
                        <a href="@Url.Action("Login")" class="text-sm font-medium hover:underline"
                            style="color: #171e54;">
                            Back to Login
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
