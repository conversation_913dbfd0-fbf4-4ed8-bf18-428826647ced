using System.Text.Json;
using Microsoft.Extensions.Options;
using Northwind.Configuration;
using Northwind.Models.Admin;
using Northwind.Services.Api;

namespace Northwind.Services.Admin;

public class AdminService : IAdminService
{
    private readonly IApiService _apiService;
    private readonly ApiSettings _apiSettings;
    private readonly ILogger<AdminService> _logger;

    public AdminService(
    IApiService apiService,
    IOptions<ApiSettings> apiSettings,
    ILogger<AdminService> logger)
    {
        _apiService = apiService;
        _apiSettings = apiSettings.Value;
        _logger = logger;
    }

    public async Task<ApprovalRequestResponse> GetApprovalRequestsAsync(
        int page = 1,
        int pageSize = 10,
        string? approvalType = null
    )
    {
        try
        {
            string endpoint = "api/v1/admin/approval-requests";

            // Add query parameters
            endpoint += $"?page={page}&pageSize={pageSize}";

            if (!string.IsNullOrEmpty(approvalType))
            {
                endpoint += $"&approvalType={approvalType}";
            }

            _logger.LogInformation("Fetching approval requests from endpoint: {Endpoint}", endpoint);

            return await _apiService.GetAsync<ApprovalRequestResponse>(endpoint);
        }
        catch (HttpRequestException ex)
            when (ex.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            // Handle authentication errors specifically
            _logger.LogError("Authentication failed while fetching approval requests: {Message}", ex.Message);
            return new ApprovalRequestResponse
            {
                Success = false,
                StatusCode = 401,
                Message = "Authentication failed. Please log in again.",
                Payload = new System.Collections.Generic.List<ApprovalRequest>(),
                Meta = new PaginationMeta
                {
                    CurrentPage = page,
                    PageSize = pageSize,
                    TotalCount = 0,
                    TotalPages = 0,
                    HasNextPage = false,
                    HasPreviousPage = false,
                },
            };
        }
        catch (HttpRequestException ex)
            when (ex.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            // Handle authorization errors specifically
            _logger.LogError("Permission denied while fetching approval requests: {Message}", ex.Message);
            return new ApprovalRequestResponse
            {
                Success = false,
                StatusCode = 403,
                Message = "You don't have permission to access this resource.",
                Payload = new System.Collections.Generic.List<ApprovalRequest>(),
                Meta = new PaginationMeta
                {
                    CurrentPage = page,
                    PageSize = pageSize,
                    TotalCount = 0,
                    TotalPages = 0,
                    HasNextPage = false,
                    HasPreviousPage = false,
                },
            };
        }
        catch (Exception ex)
        {
            // Return an empty response with error message for other exceptions
            _logger.LogError("Failed to retrieve approval requests: {Message}", ex.Message);
            return new ApprovalRequestResponse
            {
                Success = false,
                Message = $"Failed to retrieve approval requests: {ex.Message}",
                Payload = new System.Collections.Generic.List<ApprovalRequest>(),
                Meta = new PaginationMeta
                {
                    CurrentPage = page,
                    PageSize = pageSize,
                    TotalCount = 0,
                    TotalPages = 0,
                    HasNextPage = false,
                    HasPreviousPage = false,
                },
            };
        }
    }

    public async Task<ApprovalActionResponse> ApproveRequestAsync(string requestId, string? notes = null)
    {
        try
        {
            if (string.IsNullOrEmpty(requestId))
            {
                throw new ArgumentException(
                    "Request ID cannot be null or empty",
                    nameof(requestId)
                );
            }

            string endpoint = $"api/v1/admin/approval-requests/{requestId}/approve";

            // Log the request for debugging
            _logger.LogInformation("Sending approval request to endpoint: {Endpoint}", endpoint);

            // Create request body with notes
            var requestBody = new ApprovalActionRequest
            {
                Notes = notes
            };

            // Log the request body
            _logger.LogInformation("Request body: Notes={Notes}", notes ?? "null");

            // Use POST with the request body
            var response = await _apiService.PostAsync<ApprovalActionRequest, ApprovalActionResponse>(
                endpoint,
                requestBody
            );

            // Log the response
            _logger.LogInformation(
                "Approval response: Success={Success}, Message={Message}",
                response.Success,
                response.Message
            );

            return response;
        }
        catch (HttpRequestException ex)
            when (ex.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            // Handle authentication errors specifically
            _logger.LogError("Authentication failed while approving request: {Message}", ex.Message);
            return new ApprovalActionResponse
            {
                Success = false,
                StatusCode = 401,
                Message = "Authentication failed. Please log in again.",
            };
        }
        catch (HttpRequestException ex)
            when (ex.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            // Handle authorization errors specifically
            _logger.LogError("Permission denied while approving request: {Message}", ex.Message);
            return new ApprovalActionResponse
            {
                Success = false,
                StatusCode = 403,
                Message = "You don't have permission to approve requests.",
            };
        }
        catch (Exception ex)
        {
            // Return an error response for other exceptions
            _logger.LogError("Failed to approve request: {Message}", ex.Message);
            return new ApprovalActionResponse
            {
                Success = false,
                Message = $"Failed to approve request: {ex.Message}",
            };
        }
    }

    public async Task<ApprovalActionResponse> RejectRequestAsync(
        string requestId,
        string? rejectionReason = null,
        string? notes = null
    )
    {
        try
        {
            if (string.IsNullOrEmpty(requestId))
            {
                throw new ArgumentException(
                    "Request ID cannot be null or empty",
                    nameof(requestId)
                );
            }

            string endpoint = $"api/v1/admin/approval-requests/{requestId}/reject";

            // Log the request for debugging
            _logger.LogInformation("Sending rejection request to endpoint: {Endpoint}", endpoint);

            // Create the request body with rejection reason and notes if provided
            object requestBody;

            if (string.IsNullOrEmpty(rejectionReason) && string.IsNullOrEmpty(notes))
            {
                // Use PostEmptyAsync for empty body
                _logger.LogInformation("No rejection reason or notes provided, using empty body");
                return await _apiService.PostEmptyAsync<ApprovalActionResponse>(endpoint);
            }
            else if (string.IsNullOrEmpty(notes))
            {
                requestBody = new { rejectionReason };
            }
            else if (string.IsNullOrEmpty(rejectionReason))
            {
                requestBody = new { notes };
            }
            else
            {
                requestBody = new { rejectionReason, notes };
            }

            // Log the request body
            _logger.LogInformation("Request body: {RequestBody}", JsonSerializer.Serialize(requestBody));

            var response = await _apiService.PostAsync<object, ApprovalActionResponse>(
                endpoint,
                requestBody
            );

            // Log the response
            _logger.LogInformation(
                "Rejection response: Success={Success}, Message={Message}",
                response.Success,
                response.Message
            );

            return response;
        }
        catch (HttpRequestException ex)
            when (ex.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            // Handle authentication errors specifically
            _logger.LogError("Authentication failed while rejecting request: {Message}", ex.Message);
            return new ApprovalActionResponse
            {
                Success = false,
                StatusCode = 401,
                Message = "Authentication failed. Please log in again.",
            };
        }
        catch (HttpRequestException ex)
            when (ex.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            // Handle authorization errors specifically
            _logger.LogError("Permission denied while rejecting request: {Message}", ex.Message);
            return new ApprovalActionResponse
            {
                Success = false,
                StatusCode = 403,
                Message = "You don't have permission to reject requests.",
            };
        }
        catch (Exception ex)
        {
            // Return an error response for other exceptions
            _logger.LogError("Failed to reject request: {Message}", ex.Message);
            return new ApprovalActionResponse
            {
                Success = false,
                Message = $"Failed to reject request: {ex.Message}",
            };
        }
    }
}
