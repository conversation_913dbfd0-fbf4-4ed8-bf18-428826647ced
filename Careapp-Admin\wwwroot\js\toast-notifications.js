/**
 * Toast Notifications
 * 
 * This script handles the display and animation of toast notifications.
 * It ensures that notifications are properly displayed even when the page is reloaded.
 */

document.addEventListener('DOMContentLoaded', function() {
    // Handle success notifications
    const successNotification = document.getElementById('success-notification');
    if (successNotification) {
        // Make sure the notification is visible
        successNotification.style.opacity = '1';
        
        // Set a timeout to fade it out
        setTimeout(function() {
            successNotification.classList.add('opacity-0');
            setTimeout(function() {
                successNotification.style.display = 'none';
            }, 1500);
        }, 3000); // Show for 3 seconds before fading out
    }
    
    // Handle error notifications
    const errorNotification = document.getElementById('error-notification');
    if (errorNotification) {
        // Make sure the notification is visible
        errorNotification.style.opacity = '1';
        
        // Set a timeout to fade it out
        setTimeout(function() {
            errorNotification.classList.add('opacity-0');
            setTimeout(function() {
                errorNotification.style.display = 'none';
            }, 1500);
        }, 3000); // Show for 3 seconds before fading out
    }
});
