using Microsoft.AspNetCore.Mvc;
using Northwind.ViewModels;

namespace Northwind.Controllers
{
    public class ErrorController : Controller
    {
        [Route("Error/{statusCode}")]
        public IActionResult HttpStatusCodeHandler(int statusCode)
        {
            var errorViewModel = new ErrorViewModel { StatusCode = statusCode };

            switch (statusCode)
            {
                case 404:
                    errorViewModel.Title = "Page Not Found";
                    errorViewModel.Message =
                        "Sorry, the page you are looking for might have been removed or is temporarily unavailable.";
                    return View("NotFound", errorViewModel);

                case 403:
                    errorViewModel.Title = "Access Restricted";
                    errorViewModel.Message =
                        "Sorry, you don't have permission to access this resource.";
                    return View("Restricted", errorViewModel);

                case 500:
                    errorViewModel.Title = "Internal Server Error";
                    errorViewModel.Message =
                        "Sorry, something went wrong on our end. We're working to fix the issue.";
                    return View("ServerError", errorViewModel);

                default:
                    errorViewModel.Title = "Error";
                    errorViewModel.Message = "An error occurred while processing your request.";
                    return View("Error", errorViewModel);
            }
        }

        [Route("Error")]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            var errorViewModel = new ErrorViewModel
            {
                StatusCode = 500,
                Title = "Internal Server Error",
                Message = "Sorry, something went wrong on our end. We're working to fix the issue.",
            };

            return View("ServerError", errorViewModel);
        }

        [Route("NotFound")]
        public IActionResult NotFound()
        {
            var errorViewModel = new ErrorViewModel
            {
                StatusCode = 404,
                Title = "Page Not Found",
                Message =
                    "Sorry, the page you are looking for might have been removed or is temporarily unavailable.",
            };

            return View(errorViewModel);
        }

        [Route("Restricted")]
        public IActionResult Restricted()
        {
            var errorViewModel = new ErrorViewModel
            {
                StatusCode = 403,
                Title = "Access Restricted",
                Message = "Sorry, you don't have permission to access this resource.",
            };

            return View(errorViewModel);
        }

        [Route("ServerError")]
        public IActionResult ServerError()
        {
            var errorViewModel = new ErrorViewModel
            {
                StatusCode = 500,
                Title = "Internal Server Error",
                Message = "Sorry, something went wrong on our end. We're working to fix the issue.",
            };

            return View(errorViewModel);
        }
    }
}
