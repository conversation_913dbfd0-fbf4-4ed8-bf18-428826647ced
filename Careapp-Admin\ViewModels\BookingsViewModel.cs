using Northwind.Models.Admin;

namespace Careapp_Admin.ViewModels;

public class BookingsViewModel
{
    public List<BookingItemViewModel> Bookings { get; set; }
    public PaginationMeta Pagination { get; set; }
    public string SearchString { get; set; }
    public string SortBy { get; set; }
    public bool SortDescending { get; set; }
    public string ErrorMessage { get; set; }
    public string SuccessMessage { get; set; }

    public BookingsViewModel()
    {
        Bookings = new List<BookingItemViewModel>();
        Pagination = new PaginationMeta();
    }

    public int PendingBookingsCount => Bookings?.Count(b => b.Status == 0) ?? 0;
    public int ConfirmedBookingsCount => Bookings?.Count(b => b.Status == 2) ?? 0;
    public int CompletedBookingsCount => Bookings?.Count(b => b.Status == 4) ?? 0;
}
