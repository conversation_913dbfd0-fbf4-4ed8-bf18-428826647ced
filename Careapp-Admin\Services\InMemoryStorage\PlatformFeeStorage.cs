namespace Northwind.Services.InMemoryStorage
{
    /// <summary>
    /// Static class to store platform fee settings in memory
    /// </summary>
    public static class PlatformFeeStorage
    {
        // Default values
        private static decimal _platformFeePercentage = 5.0m;
        private static decimal _serviceFeePercentage = 2.5m;
        private static decimal _minimumFeeAmount = 1.0m;
        private static decimal _maximumFeeAmount = 100.0m;
        private static bool _applyFeesToAllServices = true;

        // Properties with getters and setters
        public static decimal PlatformFeePercentage
        {
            get { return _platformFeePercentage; }
            set { _platformFeePercentage = value; }
        }

        public static decimal ServiceFeePercentage
        {
            get { return _serviceFeePercentage; }
            set { _serviceFeePercentage = value; }
        }

        public static decimal MinimumFeeAmount
        {
            get { return _minimumFeeAmount; }
            set { _minimumFeeAmount = value; }
        }

        public static decimal MaximumFeeAmount
        {
            get { return _maximumFeeAmount; }
            set { _maximumFeeAmount = value; }
        }

        public static bool ApplyFeesToAllServices
        {
            get { return _applyFeesToAllServices; }
            set { _applyFeesToAllServices = value; }
        }
    }
}
