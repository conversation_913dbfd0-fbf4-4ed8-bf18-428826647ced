using System.Net;
using Microsoft.Extensions.Options;
using Northwind.Configuration;
using Northwind.Models.Services;
using Northwind.Services.Api;

namespace Northwind.Services.Services;

public class ServicesService : IServicesService
{
    private readonly IApiService _apiService;
    private readonly ApiSettings _apiSettings;
    private readonly ILogger<ServicesService> _logger;
    private const string BaseEndpoint = "api/v1/carecategories";

    public ServicesService(
        IApiService apiService,
        IOptions<ApiSettings> apiSettings,
        ILogger<ServicesService> logger
    )
    {
        _apiService = apiService ?? throw new ArgumentNullException(nameof(apiService));
        _apiSettings = apiSettings?.Value ?? throw new ArgumentNullException(nameof(apiSettings));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<CareCategoryResponse> GetCareCategoriesAsync(bool includeInactive = true)
    {
        try
        {
            string endpoint = $"{BaseEndpoint}?includeInactive={includeInactive}";
            return await _apiService.GetAsync<CareCategoryResponse>(endpoint);
        }
        catch (HttpRequestException ex) when (ex.StatusCode == HttpStatusCode.Unauthorized)
        {
            _logger.LogWarning(ex, "Authentication failed while retrieving care categories.");
            return CreateErrorResponse(401, "Authentication failed. Please log in again.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to retrieve care categories.");
            return CreateErrorResponse(500, $"Failed to retrieve care categories: {ex.Message}");
        }
    }

    public async Task<bool> ActivateCategoryAsync(string categoryId)
    {
        return await ExecuteCategoryOperationAsync(categoryId, "activate", "activating");
    }

    public async Task<bool> DeactivateCategoryAsync(string categoryId)
    {
        return await ExecuteCategoryOperationAsync(categoryId, "deactivate", "deactivating");
    }

    public async Task<bool> AddCategoryAsync(string name, string description)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(name))
            {
                throw new ArgumentException("Category name cannot be null or empty", nameof(name));
            }

            string endpoint = BaseEndpoint;
            var requestBody = new
            {
                name,
                description,
                isActive = false,
            };

            await _apiService.PostAsync<object, dynamic>(endpoint, requestBody);
            return true;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error adding category.");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding category.");
            return false;
        }
    }

    public async Task<bool> DeleteCategoryAsync(string categoryId)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(categoryId))
            {
                throw new ArgumentException(
                    "Category ID cannot be null or empty",
                    nameof(categoryId)
                );
            }

            string endpoint = $"{BaseEndpoint}/{categoryId}";
            return await _apiService.DeleteAsync(endpoint);
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error deleting category.");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting category.");
            return false;
        }
    }

    private async Task<bool> ExecuteCategoryOperationAsync(
        string categoryId,
        string operation,
        string operationName
    )
    {
        try
        {
            if (string.IsNullOrWhiteSpace(categoryId))
            {
                throw new ArgumentException(
                    "Category ID cannot be null or empty",
                    nameof(categoryId)
                );
            }

            string endpoint = $"{BaseEndpoint}/{categoryId}/{operation}";
            await _apiService.PostAsync<object, dynamic>(endpoint, new { });
            return true;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, $"HTTP error {operationName} category.");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error {operationName} category.");
            return false;
        }
    }

    private static CareCategoryResponse CreateErrorResponse(int statusCode, string message)
    {
        return new CareCategoryResponse
        {
            Success = false,
            StatusCode = statusCode,
            Message = message,
            Payload = new List<CareCategory>(),
        };
    }

    public async Task<bool> UpdateCategoryAsync(
        string categoryId,
        string name,
        string description,
        bool isActive
    )
    {
        try
        {
            if (string.IsNullOrWhiteSpace(categoryId))
            {
                throw new ArgumentException(
                    "Category ID cannot be null or empty",
                    nameof(categoryId)
                );
            }

            if (string.IsNullOrWhiteSpace(name))
            {
                throw new ArgumentException("Category name cannot be null or empty", nameof(name));
            }

            string endpoint = $"{BaseEndpoint}/{categoryId}";
            var requestBody = new
            {
                name,
                description,
                isActive,
            };

            await _apiService.PutAsync<object, dynamic>(endpoint, requestBody);
            return true;
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError(ex, "HTTP error adding category.");
            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding category.");
            return false;
        }
    }
}
