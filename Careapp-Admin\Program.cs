using Northwind.Extensions;
using Northwind.Middleware;

var builder = WebApplication.CreateBuilder(args);

builder.Services.AddControllersWithViews();

// Configure JWT authentication and API services
builder.Services.ConfigureJwtAuthentication(builder.Configuration);
builder.Services.ConfigureApiServices(builder.Configuration);

builder.Services.ConfigureRailwayPattern();
builder.Services.ConfigureAuthorizationPolicy();
builder.Services.ConfigureMediatR();
builder.Services.ConfigureApiClient();

var app = builder.Build();
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
}
else
{
    app.UseDeveloperExceptionPage();
}

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();

app.UseAuthorization();

app.UseTokenRefresh();

// Use JWT authentication middleware
app.UseJwtAuthentication();

// Configure custom error pages
app.UseStatusCodePagesWithReExecute("/Error/{0}");
app.MapControllerRoute(name: "default", pattern: "{controller=Dashboard}/{action=Index}/{id?}");

app.Run();
