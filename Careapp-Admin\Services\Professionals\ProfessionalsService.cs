using System.Text.Json;
using Microsoft.Extensions.Options;
using Northwind.Configuration;
using Northwind.Models.Admin;
using Northwind.Models.Professionals;
using Northwind.Services.Api;

namespace Northwind.Services.Professionals;

public class ProfessionalsService : IProfessionalsService
{
    private readonly IApiService _apiService;
    private readonly ApiSettings _apiSettings;
    private readonly ILogger<ProfessionalsService> _logger;

    public ProfessionalsService(
        IApiService apiService,
        IOptions<ApiSettings> apiSettings,
        ILogger<ProfessionalsService> logger)
    {
        _apiService = apiService;
        _apiSettings = apiSettings.Value;
        _logger = logger;
    }

    public async Task<CareProviderResponse> GetCareProvidersAsync(
        int page = 1,
        int pageSize = 10,
        string searchTerm = "",
        string sortBy = "createdAt",
        bool sortDescending = true
    )
    {
        try
        {
            // Build the endpoint URL with query parameters
            string endpoint =
                $"api/v1/admin/user-profiles?PageNumber={page}&PageSize={pageSize}";

            // Add optional parameters if provided
            if (!string.IsNullOrWhiteSpace(searchTerm))
            {
                endpoint += $"&SearchTerm={Uri.EscapeDataString(searchTerm)}";
            }

            endpoint += $"&SortBy={Uri.EscapeDataString(sortBy)}";
            endpoint += $"&SortDescending={sortDescending}";

            // Log the endpoint for debugging
            _logger.LogInformation("Calling API endpoint: {Endpoint}", endpoint);

            // Add the UserType header
            var headers = new Dictionary<string, string> { { "UserType", "CareProvider" } };

            return await _apiService.GetWithHeadersAsync<CareProviderResponse>(
                endpoint,
                headers
            );
        }
        catch (HttpRequestException ex)
            when (ex.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            // Handle authentication errors specifically
            _logger.LogError("Authentication failed while fetching care providers: {Message}", ex.Message);
            return new CareProviderResponse
            {
                Success = false,
                StatusCode = 401,
                Message = "Authentication failed. Please log in again.",
                Payload = new List<CareProvider>(),
                Meta = new PaginationMeta
                {
                    CurrentPage = page,
                    PageSize = pageSize,
                    TotalCount = 0,
                    TotalPages = 0,
                    HasNextPage = false,
                    HasPreviousPage = false,
                },
            };
        }
        catch (Exception ex)
        {
            // Log the exception for debugging
            _logger.LogError("Exception in GetCareProvidersAsync: {Message}, Stack trace: {StackTrace}", ex.Message,
                ex.StackTrace);

            // Return an empty response with error message
            return new CareProviderResponse
            {
                Success = false,
                Message = $"Failed to retrieve care providers: {ex.Message}",
                Payload = new List<CareProvider>(),
                Meta = new PaginationMeta
                {
                    CurrentPage = page,
                    PageSize = pageSize,
                    TotalCount = 0,
                    TotalPages = 0,
                    HasNextPage = false,
                    HasPreviousPage = false,
                },
            };
        }
    }

    public async Task<ApprovalRequestResponse> GetProviderApprovalRequestsAsync(string userId)
    {
        try
        {
            if (string.IsNullOrEmpty(userId))
            {
                _logger.LogError("User ID is null or empty in GetProviderApprovalRequestsAsync");
                return new ApprovalRequestResponse
                {
                    Success = false,
                    StatusCode = 400,
                    Message = "User ID cannot be null or empty",
                    Payload = new List<ApprovalRequest>(),
                };
            }

            // Build the endpoint URL with query parameters
            string endpoint =
                $"api/v1/admin/approval-requests?ApprovalType=CareProviderVerification&UserId={Uri.EscapeDataString(userId)}";

            // Log the request for debugging
            _logger.LogInformation("=== APPROVAL REQUESTS API REQUEST START ===");
            _logger.LogInformation("Timestamp: {Timestamp:yyyy-MM-dd HH:mm:ss} UTC", DateTime.UtcNow);
            _logger.LogInformation("User ID: {UserId}", userId);
            _logger.LogInformation("Base URL: {BaseUrl}", _apiSettings.BaseUrl);
            _logger.LogInformation("Full endpoint: {FullEndpoint}", $"{_apiSettings.BaseUrl}{endpoint}");

            // Create headers with UserType: CareProvider
            var headers = new Dictionary<string, string> { { "UserType", "CareProvider" } };

            _logger.LogInformation("Request Headers:");
            foreach (var header in headers)
            {
                _logger.LogInformation("  {Key}: {Value}", header.Key, header.Value);
            }

            // Call the API with headers
            var response = await _apiService.GetWithHeadersAsync<ApprovalRequestResponse>(
                endpoint,
                headers
            );

            // Log the response
            _logger.LogInformation("=== APPROVAL REQUESTS API RESPONSE ===");
            if (response != null)
            {
                _logger.LogInformation("Response Success: {Success}", response.Success);
                _logger.LogInformation("Response Status Code: {StatusCode}", response.StatusCode);
                _logger.LogInformation("Response Message: {Message}", response.Message);

                if (response.Payload != null)
                {
                    _logger.LogInformation("Payload Count: {Count}", response.Payload.Count);
                    _logger.LogInformation("Approval Requests Preview:");
                    foreach (var request in response.Payload.Take(3))
                    {
                        _logger.LogInformation(
                            "  - Request ID: {Id}, User ID: {UserId}, Status: {Status}",
                            request.Id,
                            request.UserId,
                            request.Status
                        );
                    }
                }
                else
                {
                    _logger.LogInformation("Payload is NULL");
                }
            }
            else
            {
                _logger.LogInformation("RESPONSE IS NULL");
            }

            _logger.LogInformation("=== APPROVAL REQUESTS API REQUEST END ===");

            return response;
        }
        catch (HttpRequestException ex)
            when (ex.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            // Handle authentication errors specifically
            _logger.LogError("Authentication failed while fetching approval requests: {Message}", ex.Message);
            return new ApprovalRequestResponse
            {
                Success = false,
                StatusCode = 401,
                Message = "Authentication failed. Please log in again.",
                Payload = new List<ApprovalRequest>(),
            };
        }
        catch (Exception ex)
        {
            // Log the exception for debugging
            _logger.LogError("Exception in GetProviderApprovalRequestsAsync: {Message}, Stack trace: {StackTrace}",
                ex.Message, ex.StackTrace);

            // Return an error response for other exceptions
            return new ApprovalRequestResponse
            {
                Success = false,
                Message = $"Failed to fetch approval requests: {ex.Message}",
                Payload = new List<ApprovalRequest>(),
            };
        }
    }

    public async Task<ApprovalActionResponse> ApproveProviderAsync(
        string requestId,
        string notes = null
    )
    {
        try
        {
            if (string.IsNullOrEmpty(requestId))
            {
                throw new ArgumentException(
                    "Request ID cannot be null or empty",
                    nameof(requestId)
                );
            }

            string endpoint = $"api/v1/admin/approval-requests/{requestId}/approve";

            // Log the request for debugging
            _logger.LogInformation("Sending approval request to endpoint: {Endpoint}", endpoint);

            // Create request body with notes
            var requestBody = new ApprovalActionRequest { Notes = notes };

            // Log the request body
            _logger.LogInformation("Request body: Notes={Notes}", notes ?? "null");

            // Use POST with the request body
            var response = await _apiService.PostAsync<
                ApprovalActionRequest,
                ApprovalActionResponse
            >(endpoint, requestBody);

            // Log the response
            _logger.LogInformation(
                "Approval response: Success={Success}, Message={Message}",
                response.Success,
                response.Message
            );

            return response;
        }
        catch (HttpRequestException ex)
            when (ex.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            // Handle authentication errors specifically
            _logger.LogError("Authentication failed while approving provider: {Message}", ex.Message);
            return new ApprovalActionResponse
            {
                Success = false,
                StatusCode = 401,
                Message = "Authentication failed. Please log in again.",
            };
        }
        catch (HttpRequestException ex)
            when (ex.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            // Handle authorization errors specifically
            _logger.LogError("Permission denied while approving provider: {Message}", ex.Message);
            return new ApprovalActionResponse
            {
                Success = false,
                StatusCode = 403,
                Message = "You don't have permission to approve providers.",
            };
        }
        catch (Exception ex)
        {
            // Log the exception for debugging
            _logger.LogError("Exception in ApproveProviderAsync: {Message}, Stack trace: {StackTrace}", ex.Message,
                ex.StackTrace);

            // Return an error response for other exceptions
            return new ApprovalActionResponse
            {
                Success = false,
                Message = $"Failed to approve provider: {ex.Message}",
            };
        }
    }

    public async Task<ApprovalActionResponse> RejectProviderAsync(
        string requestId,
        string rejectionReason,
        string notes = null
    )
    {
        try
        {
            if (string.IsNullOrEmpty(requestId))
            {
                throw new ArgumentException(
                    "Request ID cannot be null or empty",
                    nameof(requestId)
                );
            }

            if (string.IsNullOrEmpty(rejectionReason))
            {
                throw new ArgumentException(
                    "Rejection reason cannot be null or empty",
                    nameof(rejectionReason)
                );
            }

            string endpoint = $"api/v1/admin/approval-requests/{requestId}/reject";

            // Log the request for debugging
            _logger.LogInformation("Sending rejection request to endpoint: {Endpoint}", endpoint);

            // Create the request body with rejection reason and notes
            object requestBody;

            if (string.IsNullOrEmpty(notes))
            {
                requestBody = new { rejectionReason };
            }
            else
            {
                requestBody = new { rejectionReason, notes };
            }

            // Log the request body
            _logger.LogInformation("Request body: {RequestBody}", JsonSerializer.Serialize(requestBody));

            var response = await _apiService.PostAsync<object, ApprovalActionResponse>(
                endpoint,
                requestBody
            );

            // Log the response
            _logger.LogInformation(
                "Rejection response: Success={Success}, Message={Message}",
                response.Success,
                response.Message
            );

            return response;
        }
        catch (HttpRequestException ex)
            when (ex.StatusCode == System.Net.HttpStatusCode.Unauthorized)
        {
            // Handle authentication errors specifically
            _logger.LogError("Authentication failed while rejecting provider: {Message}", ex.Message);
            return new ApprovalActionResponse
            {
                Success = false,
                StatusCode = 401,
                Message = "Authentication failed. Please log in again.",
            };
        }
        catch (HttpRequestException ex)
            when (ex.StatusCode == System.Net.HttpStatusCode.Forbidden)
        {
            // Handle authorization errors specifically
            _logger.LogError("Permission denied while rejecting provider: {Message}", ex.Message);
            return new ApprovalActionResponse
            {
                Success = false,
                StatusCode = 403,
                Message = "You don't have permission to reject providers.",
            };
        }
        catch (Exception ex)
        {
            // Log the exception for debugging
            _logger.LogError("Exception in RejectProviderAsync: {Message}, Stack trace: {StackTrace}", ex.Message,
                ex.StackTrace);

            // Return an error response for other exceptions
            return new ApprovalActionResponse
            {
                Success = false,
                Message = $"Failed to reject provider: {ex.Message}",
            };
        }
    }
}