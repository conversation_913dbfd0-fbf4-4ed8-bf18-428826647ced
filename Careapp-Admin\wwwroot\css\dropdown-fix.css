/* Dropdown positioning fixes */
.dropdown-container {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    z-index: 50;
    min-width: 14rem;
    max-height: calc(100vh - 100px);
    overflow-y: auto;
}

/* Ensure dropdowns are visible in scrollable containers */
.overflow-x-auto,
.overflow-y-auto,
.overflow-auto {
    overflow: visible !important;
}

/* Make table cells that contain dropdowns have overflow visible */
td.whitespace-nowrap {
    overflow: visible !important;
}

/* Ensure the dropdown container has proper stacking context */
.dropdown-container {
    z-index: 40;
}

/* When dropdown is open, increase z-index */
.dropdown-container[x-data*="open: true"] {
    z-index: 50;
}
