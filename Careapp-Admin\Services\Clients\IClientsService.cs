using System.Threading.Tasks;
using Northwind.Models.Clients;

namespace Northwind.Services.Clients
{
    public interface IClientsService
    {
        /// <summary>
        /// Gets a paginated list of clients from the API
        /// </summary>
        /// <param name="page">Page number (1-based)</param>
        /// <param name="pageSize">Number of items per page</param>
        /// <param name="searchTerm">Optional search term to filter clients</param>
        /// <param name="sortBy">Field to sort by (e.g., "name", "createdAt")</param>
        /// <param name="sortDescending">Whether to sort in descending order</param>
        /// <returns>Client response with pagination metadata</returns>
        Task<ClientResponse> GetClientsAsync(
            int page = 1,
            int pageSize = 5,
            string searchTerm = "",
            string sortBy = "createdAt",
            bool sortDescending = true
        );

        /// <summary>
        /// Gets a specific client by ID
        /// </summary>
        /// <param name="clientId">The client's user ID</param>
        /// <returns>Client response with single client</returns>
        Task<ClientResponse> GetClientByIdAsync(string clientId);

        /// <summary>
        /// Gets a client's detailed profile by ID
        /// </summary>
        /// <param name="clientId">The client's user ID</param>
        /// <returns>Client profile response with detailed information</returns>
        Task<ClientProfileResponse> GetClientProfileAsync(string clientId);

        /// <summary>
        /// Updates a client's status (Active, Inactive, Suspended)
        /// </summary>
        /// <param name="clientId">The client's user ID</param>
        /// <param name="status">New status</param>
        /// <returns>True if successful</returns>
        Task<bool> UpdateClientStatusAsync(string clientId, string status);

        /// <summary>
        /// Deletes a client
        /// </summary>
        /// <param name="clientId">The client's user ID</param>
        /// <returns>True if successful</returns>
        Task<bool> DeleteClientAsync(string clientId);
    }
}
