using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace Northwind.Models.Admin
{
    public class ApprovalRequestResponse
    {
        [JsonPropertyName("apiResponseId")]
        public string ApiResponseId { get; set; }
        
        [JsonPropertyName("success")]
        public bool Success { get; set; }
        
        [JsonPropertyName("statusCode")]
        public int StatusCode { get; set; }
        
        [JsonPropertyName("message")]
        public string Message { get; set; }
        
        [JsonPropertyName("payload")]
        public List<ApprovalRequest> Payload { get; set; }
        
        [JsonPropertyName("meta")]
        public PaginationMeta Meta { get; set; }
        
        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; }
    }
    
    public class ApprovalRequest
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }
        
        [JsonPropertyName("userId")]
        public string UserId { get; set; }
        
        [Json<PERSON>ropertyName("email")]
        public string Email { get; set; }
        
        [JsonPropertyName("name")]
        public string Name { get; set; }
        
        [JsonPropertyName("approvalType")]
        public string ApprovalType { get; set; }
        
        [JsonPropertyName("approvalTypeDescription")]
        public string ApprovalTypeDescription { get; set; }
        
        [JsonPropertyName("isApproved")]
        public bool IsApproved { get; set; }
        
        [JsonPropertyName("status")]
        public string Status { get; set; }
        
        [JsonPropertyName("rejectionReason")]
        public string RejectionReason { get; set; }
        
        [JsonPropertyName("approvalData")]
        public string ApprovalData { get; set; }
        
        [JsonPropertyName("relatedEntityId")]
        public string RelatedEntityId { get; set; }
        
        [JsonPropertyName("processedBy")]
        public string ProcessedBy { get; set; }
        
        [JsonPropertyName("processedByName")]
        public string ProcessedByName { get; set; }
        
        [JsonPropertyName("processedAt")]
        public DateTime? ProcessedAt { get; set; }
        
        [JsonPropertyName("notes")]
        public string Notes { get; set; }
        
        [JsonPropertyName("createdAt")]
        public DateTime CreatedAt { get; set; }
    }
    
    public class PaginationMeta
    {
        [JsonPropertyName("currentPage")]
        public int CurrentPage { get; set; }
        
        [JsonPropertyName("totalPages")]
        public int TotalPages { get; set; }
        
        [JsonPropertyName("totalCount")]
        public int TotalCount { get; set; }
        
        [JsonPropertyName("pageSize")]
        public int PageSize { get; set; }
        
        [JsonPropertyName("hasPreviousPage")]
        public bool HasPreviousPage { get; set; }
        
        [JsonPropertyName("hasNextPage")]
        public bool HasNextPage { get; set; }
    }
}
