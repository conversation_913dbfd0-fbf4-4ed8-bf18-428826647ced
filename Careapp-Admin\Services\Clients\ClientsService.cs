using Microsoft.Extensions.Options;
using Northwind.Configuration;
using Northwind.Models.Clients;
using Northwind.Services.Api;

namespace Northwind.Services.Clients;

public class ClientsService : IClientsService
{
    private readonly IApiService _apiService;
    private readonly ApiSettings _apiSettings;
    private readonly ILogger<ClientsService> _logger;

    public ClientsService(
        IApiService apiService,
        IOptions<ApiSettings> apiSettings,
        ILogger<ClientsService> logger
    )
    {
        _apiService = apiService;
        _apiSettings = apiSettings.Value;
        _logger = logger;
    }

    public async Task<ClientResponse> GetClientsAsync(
        int page = 1,
        int pageSize = 5,
        string searchTerm = "",
        string sortBy = "createdAt",
        bool sortDescending = true
    )
    {
        try
        {
            // Comprehensive logging before building the endpoint
            _logger.LogInformation("=== CLIENT API REQUEST PARAMETERS ===");
            _logger.LogInformation(
                "Timestamp: {Timestamp:yyyy-MM-dd HH:mm:ss} UTC",
                DateTime.UtcNow
            );
            _logger.LogInformation("Input Parameters:");
            _logger.LogInformation("  - PageNumber: {Page}", page);
            _logger.LogInformation("  - PageSize: {PageSize}", pageSize);
            _logger.LogInformation("  - SearchTerm: '{SearchTerm}'", searchTerm);
            _logger.LogInformation("  - SortBy: '{SortBy}'", sortBy);
            _logger.LogInformation("  - SortDescending: {SortDescending}", sortDescending);

            // Build the endpoint with query parameters for account profiles
            var endpoint = $"api/v1/account/profiles?pageNumber={page}&pageSize={pageSize}";

            // Add search parameter if provided
            if (!string.IsNullOrEmpty(searchTerm))
            {
                endpoint += $"&searchTerm={Uri.EscapeDataString(searchTerm)}";
            }

            // Add sorting parameters
            endpoint += $"&sortBy={Uri.EscapeDataString(sortBy)}";
            endpoint += $"&sortOrder={Uri.EscapeDataString(sortDescending ? "desc" : "asc")}";

            // Comprehensive logging of the final endpoint
            _logger.LogInformation("=== CLIENT API REQUEST URL ===");
            _logger.LogInformation("Base URL: {BaseUrl}", _apiSettings.BaseUrl);
            _logger.LogInformation(
                "Full endpoint: {FullEndpoint}",
                $"{_apiSettings.BaseUrl}{endpoint}"
            );
            _logger.LogInformation("Parsed URL parameters:");
            _logger.LogInformation("  - pageNumber={Page}", page);
            _logger.LogInformation("  - pageSize={PageSize}", pageSize);
            if (!string.IsNullOrEmpty(searchTerm))
            {
                _logger.LogInformation(
                    "  - searchTerm={SearchTerm}",
                    Uri.EscapeDataString(searchTerm)
                );
            }

            _logger.LogInformation("  - sortBy={SortBy}", Uri.EscapeDataString(sortBy));
            _logger.LogInformation(
                "  - sortOrder={SortOrder}",
                Uri.EscapeDataString(sortDescending ? "desc" : "asc")
            );

            // Create headers with UserType: Client
            var headers = new Dictionary<string, string> { { "UserType", "Client" } };

            _logger.LogInformation("Request Headers:");
            foreach (var header in headers)
            {
                _logger.LogInformation("  {Key}: {Value}", header.Key, header.Value);
            }

            _logger.LogInformation("Calling API with headers...");

            // Call the API with headers
            var response = await _apiService.GetWithHeadersAsync<ClientResponse>(endpoint, headers);

            // Log response details
            _logger.LogInformation("=== CLIENT API RESPONSE ===");
            if (response != null)
            {
                _logger.LogInformation("Response Success: {Success}", response.Success);
                _logger.LogInformation("Response Status Code: {StatusCode}", response.StatusCode);
                _logger.LogInformation("Response Message: {Message}", response.Message);
                _logger.LogInformation("Response API ID: {ApiResponseId}", response.ApiResponseId);
                _logger.LogInformation("Response Timestamp: {Timestamp}", response.Timestamp);

                if (response.Payload != null)
                {
                    _logger.LogInformation("Payload Count: {Count}", response.Payload.Count);
                    _logger.LogInformation("Client Data Preview:");
                    foreach (var client in response.Payload.Take(3))
                    {
                        _logger.LogInformation(
                            "  - Client: {Name} ({Email}) - Status: {Status}",
                            client.Name,
                            client.Email,
                            client.Status
                        );
                    }
                }
                else
                {
                    _logger.LogInformation("Payload is NULL");
                }

                if (response.Meta != null)
                {
                    _logger.LogInformation("=== PAGINATION DETAILS ===");
                    _logger.LogInformation(
                        "Current Page Number: {CurrentPage}",
                        response.Meta.CurrentPage
                    );
                    _logger.LogInformation("Page Size: {PageSize}", response.Meta.PageSize);
                    _logger.LogInformation("Total Pages: {TotalPages}", response.Meta.TotalPages);
                    _logger.LogInformation("Total Count: {TotalCount}", response.Meta.TotalCount);
                    _logger.LogInformation(
                        "Has Previous Page: {HasPreviousPage}",
                        response.Meta.HasPreviousPage
                    );
                    _logger.LogInformation(
                        "Has Next Page: {HasNextPage}",
                        response.Meta.HasNextPage
                    );

                    // Verify the page size is being applied correctly
                    if (response.Meta.PageSize != pageSize)
                    {
                        _logger.LogWarning(
                            "Requested page size ({RequestedPageSize}) does not match response page size ({ResponsePageSize})",
                            pageSize,
                            response.Meta.PageSize
                        );
                    }

                    // Verify the current page is correct
                    if (response.Meta.CurrentPage != page)
                    {
                        _logger.LogWarning(
                            "Requested pageNumber ({RequestedPage}) does not match response current page ({ResponsePage})",
                            page,
                            response.Meta.CurrentPage
                        );
                    }
                }
                else
                {
                    _logger.LogInformation("Meta (pagination) is NULL");
                }
            }
            else
            {
                _logger.LogInformation("RESPONSE IS NULL");
            }

            _logger.LogInformation("=== CLIENT API REQUEST END ===");

            return response
                ?? new ClientResponse
                {
                    Success = false,
                    StatusCode = 500,
                    Message = "No response received from API",
                    Payload = new List<Client>(),
                };
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError("=== HTTP REQUEST EXCEPTION ===");
            _logger.LogError("Exception Type: {ExceptionType}", ex.GetType().Name);
            _logger.LogError("Message: {Message}", ex.Message);
            _logger.LogError("Data: {Data}", ex.Data);
            _logger.LogError("Stack Trace: {StackTrace}", ex.StackTrace);

            // Try to extract status code from HttpRequestException
            var statusCode = 500;
            if (ex.Message.Contains("401"))
            {
                statusCode = 401;
                _logger.LogError("DETECTED 401 UNAUTHORIZED - Authentication issue");
            }
            else if (ex.Message.Contains("404"))
            {
                statusCode = 404;
                _logger.LogError("DETECTED 404 NOT FOUND - Endpoint may not exist");
            }
            else if (ex.Message.Contains("403"))
            {
                statusCode = 403;
                _logger.LogError("DETECTED 403 FORBIDDEN - Permission issue");
            }

            _logger.LogError("=== END HTTP EXCEPTION ===");

            return new ClientResponse
            {
                Success = false,
                StatusCode = statusCode,
                Message = $"HTTP error: {ex.Message}",
                Payload = new List<Client>(),
            };
        }
        catch (TaskCanceledException ex)
        {
            _logger.LogError("=== TASK CANCELED EXCEPTION (TIMEOUT) ===");
            _logger.LogError("Message: {Message}", ex.Message);
            _logger.LogError(
                "Canceled: {IsCancellationRequested}",
                ex.CancellationToken.IsCancellationRequested
            );
            _logger.LogError("This usually indicates a timeout or network issue");
            _logger.LogError("=== END TIMEOUT EXCEPTION ===");

            return new ClientResponse
            {
                Success = false,
                StatusCode = 408,
                Message = "Request timeout - API did not respond in time",
                Payload = new List<Client>(),
            };
        }
        catch (Exception ex)
        {
            _logger.LogError("=== GENERAL EXCEPTION ===");
            _logger.LogError("Exception Type: {ExceptionType}", ex.GetType().Name);
            _logger.LogError("Message: {Message}", ex.Message);
            _logger.LogError("Inner Exception: {InnerException}", ex.InnerException?.Message);
            _logger.LogError("Stack Trace: {StackTrace}", ex.StackTrace);
            _logger.LogError("=== END GENERAL EXCEPTION ===");

            return new ClientResponse
            {
                Success = false,
                StatusCode = 500,
                Message = $"An error occurred: {ex.Message}",
                Payload = new List<Client>(),
            };
        }
    }

    public async Task<ClientResponse> GetClientByIdAsync(string clientId)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
            {
                return new ClientResponse
                {
                    Success = false,
                    StatusCode = 400,
                    Message = "Client ID is required",
                    Payload = new List<Client>(),
                };
            }

            var endpoint = $"api/v1/account/profiles/{clientId}";
            _logger.LogInformation("Calling API endpoint: {Endpoint}", endpoint);

            // Create headers with UserType: Client
            var headers = new Dictionary<string, string> { { "UserType", "Client" } };

            var response = await _apiService.GetWithHeadersAsync<ClientResponse>(endpoint, headers);
            return response;
        }
        catch (Exception ex)
        {
            _logger.LogError("Exception in GetClientByIdAsync: {Message}", ex.Message);
            return new ClientResponse
            {
                Success = false,
                StatusCode = 500,
                Message = $"An error occurred: {ex.Message}",
                Payload = new List<Client>(),
            };
        }
    }

    public async Task<ClientProfileResponse> GetClientProfileAsync(string clientId)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
            {
                return new ClientProfileResponse
                {
                    Success = false,
                    StatusCode = 400,
                    Message = "Client ID is required",
                };
            }

            var endpoint = $"api/v1/account/users/{clientId}/profile";

            _logger.LogInformation("=== CLIENT PROFILE API REQUEST START ===");
            _logger.LogInformation(
                "Timestamp: {Timestamp:yyyy-MM-dd HH:mm:ss} UTC",
                DateTime.UtcNow
            );
            _logger.LogInformation("Client ID: {ClientId}", clientId);
            _logger.LogInformation(
                "Full endpoint: {FullEndpoint}",
                $"{_apiSettings.BaseUrl}{endpoint}"
            );

            // Create headers with UserType: Client
            var headers = new Dictionary<string, string> { { "UserType", "Client" } };

            _logger.LogInformation("Request Headers:");
            foreach (var header in headers)
            {
                _logger.LogInformation("  {Key}: {Value}", header.Key, header.Value);
            }

            _logger.LogInformation("Calling API for client profile...");

            var response = await _apiService.GetWithHeadersAsync<ClientProfileResponse>(
                endpoint,
                headers
            );

            _logger.LogInformation("=== CLIENT PROFILE API RESPONSE ===");
            if (response != null)
            {
                _logger.LogInformation("Response Success: {Success}", response.Success);
                _logger.LogInformation("Response Status Code: {StatusCode}", response.StatusCode);
                _logger.LogInformation("Response Message: {Message}", response.Message);

                if (response.Payload != null)
                {
                    _logger.LogInformation("Profile Name: {Name}", response.Payload.Name);
                    _logger.LogInformation("Profile Email: {Email}", response.Payload.Email);
                    _logger.LogInformation(
                        "Profile Phone: {PhoneNumber}",
                        response.Payload.PhoneNumber
                    );
                    _logger.LogInformation("Profile Gender: {Gender}", response.Payload.Gender);
                    _logger.LogInformation(
                        "Profile DOB: {DateOfBirth}",
                        response.Payload.DateOfBirth
                    );

                    if (response.Payload.PrimaryAddress != null)
                    {
                        _logger.LogInformation(
                            "Address: {StreetAddress}, {City}, {State} {PostalCode}",
                            response.Payload.PrimaryAddress.StreetAddress,
                            response.Payload.PrimaryAddress.City,
                            response.Payload.PrimaryAddress.State,
                            response.Payload.PrimaryAddress.PostalCode
                        );
                    }

                    _logger.LogInformation(
                        "Documents Count: {Count}",
                        response.Payload.Documents?.Count ?? 0
                    );
                }
                else
                {
                    _logger.LogInformation("Profile Payload is NULL");
                }
            }
            else
            {
                _logger.LogInformation("PROFILE RESPONSE IS NULL");
            }

            _logger.LogInformation("=== CLIENT PROFILE API REQUEST END ===");

            return response
                ?? new ClientProfileResponse
                {
                    Success = false,
                    StatusCode = 500,
                    Message = "No response received from API",
                };
        }
        catch (HttpRequestException ex)
        {
            _logger.LogError("=== CLIENT PROFILE HTTP REQUEST EXCEPTION ===");
            _logger.LogError("Exception Type: {ExceptionType}", ex.GetType().Name);
            _logger.LogError("Message: {Message}", ex.Message);
            _logger.LogError("Stack Trace: {StackTrace}", ex.StackTrace);

            var statusCode = 500;
            if (ex.Message.Contains("401"))
            {
                statusCode = 401;
                _logger.LogError("DETECTED 401 UNAUTHORIZED - Authentication issue");
            }
            else if (ex.Message.Contains("404"))
            {
                statusCode = 404;
                _logger.LogError("DETECTED 404 NOT FOUND - Client profile not found");
            }

            _logger.LogError("=== END CLIENT PROFILE HTTP EXCEPTION ===");

            return new ClientProfileResponse
            {
                Success = false,
                StatusCode = statusCode,
                Message = $"HTTP error: {ex.Message}",
            };
        }
        catch (Exception ex)
        {
            _logger.LogError("=== CLIENT PROFILE GENERAL EXCEPTION ===");
            _logger.LogError("Exception Type: {ExceptionType}", ex.GetType().Name);
            _logger.LogError("Message: {Message}", ex.Message);
            _logger.LogError("Inner Exception: {InnerException}", ex.InnerException?.Message);
            _logger.LogError("Stack Trace: {StackTrace}", ex.StackTrace);
            _logger.LogError("=== END CLIENT PROFILE GENERAL EXCEPTION ===");

            return new ClientProfileResponse
            {
                Success = false,
                StatusCode = 500,
                Message = $"An error occurred: {ex.Message}",
            };
        }
    }

    public async Task<bool> UpdateClientStatusAsync(string clientId, string status)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(status))
            {
                _logger.LogError("Client ID and status are required");
                return false;
            }

            var endpoint = $"api/v1/account/profiles/{clientId}/status";
            var requestData = new { status = status };

            _logger.LogInformation(
                "Updating client {ClientId} status to {Status}",
                clientId,
                status
            );

            var response = await _apiService.PutAsync<object, object>(endpoint, requestData);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError("Exception in UpdateClientStatusAsync: {Message}", ex.Message);
            return false;
        }
    }

    public async Task<bool> DeleteClientAsync(string clientId)
    {
        try
        {
            if (string.IsNullOrEmpty(clientId))
            {
                _logger.LogError("Client ID is required");
                return false;
            }

            var endpoint = $"api/v1/account/profiles/{clientId}";
            _logger.LogInformation("Deleting client {ClientId}", clientId);

            return await _apiService.DeleteAsync(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError("Exception in DeleteClientAsync: {Message}", ex.Message);
            return false;
        }
    }
}
