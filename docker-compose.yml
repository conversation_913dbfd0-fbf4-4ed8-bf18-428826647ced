version: '3.8'

services:
  careapp-admin:
    image: ${DOCKER_REGISTRY-}careapp-admin
    build:
      context: .
      dockerfile: Careapp-Admin/Dockerfile
      args:
        - BUILD_CONFIGURATION=Release
    environment:
      - ASPNETCORE_ENVIRONMENT=Production
      - ASPNETCORE_URLS=http://+:7879
      - ApiSettings__BaseUrl=${API_BASE_URL:-http://careappapi.intellexio.com/}
      - ApiSettings__AuthEndpoint=${API_AUTH_ENDPOINT:-api/v1/auth/login}
      - ApiSettings__RefreshTokenEndpoint=${API_REFRESH_TOKEN_ENDPOINT:-api/v1/auth/refresh-token}
      - ApiSettings__LogoutEndpoint=${API_LOGOUT_ENDPOINT:-api/v1/auth/logout}
      - ApiSettings__RefreshTokenThresholdMinutes=262800
      - JwtSettings__Secret=${JWT_SECRET:-SuperSecureKeyWithAtLeast32Characters123}
      - JwtSettings__TokenCookieName=${JWT_TOKEN_COOKIE_NAME:-jwt_token}
      - JwtSettings__RefreshTokenCookieName=${JWT_REFRESH_TOKEN_COOKIE_NAME:-refresh_token}
      - JwtSettings__Issuer=${JWT_ISSUER:-SuperCareApp}
      - JwtSettings__Audience=${JWT_AUDIENCE:-SuperCareAppUsers}
    ports:
      - "7879:7879"
    env_file:
      - .env
