using System.Collections.Generic;
using System.Threading.Tasks;

namespace Northwind.Services.Api
{
    public interface IApiService
    {
        Task<T> GetAsync<T>(string endpoint);
        Task<T> GetWithHeadersAsync<T>(string endpoint, Dictionary<string, string> headers);
        Task<TResponse> PostAsync<TRequest, TResponse>(string endpoint, TRequest data);
        Task<TResponse> PutAsync<TRequest, TResponse>(string endpoint, TRequest data);
        Task<bool> DeleteAsync(string endpoint);
        Task<TResponse> PostEmptyAsync<TResponse>(string endpoint);
        Task<TResponse> PostEmptyStringAsync<TResponse>(string endpoint);
    }
}
