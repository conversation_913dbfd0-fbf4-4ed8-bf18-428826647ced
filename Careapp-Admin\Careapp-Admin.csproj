<Project Sdk="Microsoft.NET.Sdk.Web">
  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <UserSecretsId>************************************</UserSecretsId>
    <DockerComposeProjectPath>..\docker-compose.dcproj</DockerComposeProjectPath>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="EfCore.SchemaCompare" Version="8.0.0" />
    <PackageReference Include="Htmx" Version="1.8.0" />
    <PackageReference Include="Htmx.TagHelpers" Version="1.8.0" />
    <PackageReference Include="MediatR" Version="12.4.1" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.6" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.22.1-Preview.1" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.2" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" Version="7.4.0" />
  </ItemGroup>
  <ItemGroup>
    <Compile Remove="wwwroot\lib\bootstrap\**" />
    <Compile Remove="Northwind\**" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Remove="wwwroot\lib\bootstrap\**" />
    <EmbeddedResource Remove="Northwind\**" />
  </ItemGroup>
  <ItemGroup>
    <Content Remove="wwwroot\lib\bootstrap\**" />
    <Content Remove="Northwind\**" />
  </ItemGroup>
  <ItemGroup>
    <None Remove="wwwroot\lib\bootstrap\**" />
    <None Remove="Northwind\**" />
  </ItemGroup>
  <ItemGroup>
    <_ContentIncludedByDefault Remove="Views\Customers\Index.cshtml" />
    <_ContentIncludedByDefault Remove="Views\Resources\Scopes.cshtml" />
    <_ContentIncludedByDefault Remove="Views\Roles\Create.cshtml" />
    <_ContentIncludedByDefault Remove="Views\Roles\Edit.cshtml" />
    <_ContentIncludedByDefault Remove="Views\Roles\Index.cshtml" />
  </ItemGroup>
  <Target Name="RestoreNpmPackages" BeforeTargets="Build">
    <Exec Command="npm install" />
  </Target>
  <Target Name="BuildTailwind" AfterTargets="RestoreNpmPackages">
    <Exec Command="npm run build-tailwind" />
  </Target>
</Project>
