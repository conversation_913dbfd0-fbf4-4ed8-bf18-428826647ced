@page
@model Careapp_Admin.ViewModels.BookingsViewModel
@{
    ViewData["Title"] = "Bookings Management";
    Layout = "Layouts/_LayoutsDash";
}

@if (Model == null)
{
    <div>Error: Model is null.</div>
    return;
}

@if (Model.Pagination == null)
{
    <div>Error: Model.Pagination is null.</div>
    return;
}

<div class="p-6">
    <!-- Enhanced Responsive Header Section -->
    <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 class="text-2xl font-bold text-gray-800">Bookings Management</h1>
        <div class="mt-4 w-full sm:w-auto sm:mt-0">
            <form asp-controller="Dashboard" asp-action="Bookings" method="get" class="flex flex-col sm:flex-row items-center gap-2">
                <div class="relative w-full sm:w-auto">
                    <input type="text" name="searchTerm" value="@Model.SearchString"
                           class="w-full sm:w-64 rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                           placeholder="Search bookings (client, service...)"
                           aria-label="Search bookings">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20"
                             fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                  clip-rule="evenodd" />
                        </svg>
                    </div>
                </div>
                <input type="hidden" name="sortBy" value="@Model.SortBy" />
                <input type="hidden" name="sortDescending" value="@Model.SortDescending.ToString().ToLower()" />
                <button type="submit"
                        class="w-full sm:w-auto rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                        aria-label="Submit search">
                    Search
                </button>
            </form>
        </div>
    </div>

    <!-- Error Messages (from Model or TempData) -->
    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="mb-6 rounded-md bg-red-50 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" /></svg>
                </div>
                <div class="ml-3"><h3 class="text-sm font-medium text-red-800">Error</h3><div class="mt-2 text-sm text-red-700"><p>@Model.ErrorMessage</p></div></div>
            </div>
        </div>
    }
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="mb-6 rounded-md bg-red-50 p-4">
            <div class="flex">
                <div class="flex-shrink-0"><svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" /></svg></div>
                <div class="ml-3"><h3 class="text-sm font-medium text-red-800">Error</h3><div class="mt-2 text-sm text-red-700"><p>@TempData["ErrorMessage"]</p></div></div>
            </div>
        </div>
    }


    <!-- Stats Cards -->
    <div class="mb-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <!-- Total Bookings Card -->
        <div class="overflow-hidden rounded-lg bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-100">
                    <svg class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>
                </div>
                <div class="ml-4">
                    <h2 class="font-semibold text-gray-500">Total Bookings</h2>
                    <p class="text-2xl font-bold text-gray-800">@(Model.Pagination?.TotalCount ?? 0)</p>
                </div>
            </div>
        </div>

        <!-- Pending Bookings Card -->
        <div class="overflow-hidden rounded-lg bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-yellow-100">
                     <svg class="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                </div>
                <div class="ml-4">
                    <h2 class="font-semibold text-gray-500">Pending</h2>
                    <p class="text-2xl font-bold text-gray-800">@Model.PendingBookingsCount</p>
                </div>
            </div>
        </div>

        <!-- Confirmed/Active Bookings Card -->
        <div class="overflow-hidden rounded-lg bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-green-100">
                    <svg class="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>
                </div>
                <div class="ml-4">
                    <h2 class="font-semibold text-gray-500">Confirmed</h2>
                    <p class="text-2xl font-bold text-gray-800">@Model.ConfirmedBookingsCount</p>
                </div>
            </div>
        </div>
        
        <!-- Completed Bookings Card -->
        <div class="overflow-hidden rounded-lg bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-gray-100">
                    <svg class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                </div>
                <div class="ml-4">
                    <h2 class="font-semibold text-gray-500">Completed</h2>
                    <p class="text-2xl font-bold text-gray-800">@Model.CompletedBookingsCount</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Dropdown Menus -->
    @if (Model.Bookings != null)
    {
        @foreach (var booking in Model.Bookings)
        {
            <div class="fixed z-50 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden"
                 role="menu" aria-orientation="vertical" aria-labelledby="menu-button-@(booking.BookingId ?? "")" id="dropdown-@(booking.BookingId ?? "")" style="z-index: 9999;">
                <div class="py-1" role="none">
                    <a href="#" onclick="viewBookingDetails('@(booking.BookingId ?? "")')" class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-900" role="menuitem">
                        <svg class="mr-3 h-5 w-5 text-indigo-600 group-hover:text-indigo-900" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                        View Details
                    </a>
                    @if (booking.Status == 0 || booking.Status == 2) // Pending or Confirmed
                    {
                        <a href="#" onclick="openCancelBookingModal('@(booking.BookingId ?? "")', '@(booking.ClientName ?? "")')" class="group flex items-center px-4 py-2 text-sm text-red-700 hover:bg-red-50 hover:text-red-900" role="menuitem">
                            <svg class="mr-3 h-5 w-5 text-red-600 group-hover:text-red-900" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg>
                            Cancel Booking
                        </a>
                    }
                    <a href="@Url.Action("EditBooking", "Dashboard", new { bookingId = booking.BookingId ?? "" })" class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-900" role="menuitem">
                        <svg class="mr-3 h-5 w-5 text-indigo-600 group-hover:text-indigo-900" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" /></svg>
                        Edit Booking
                    </a>
                </div>
                 @if (booking.Status == 2) // Only if Confirmed/Active
                {
                    <div class="py-1" role="none">
                        <a href="#" onclick="markAsComplete('@(booking.BookingId ?? "")')" class="group flex items-center px-4 py-2 text-sm text-green-700 hover:bg-green-50 hover:text-green-900" role="menuitem">
                            <svg class="mr-3 h-5 w-5 text-green-600 group-hover:text-green-900" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" /></svg>
                            Mark as Complete
                        </a>
                    </div>
                }
            </div>
        }
    }

    <!-- Bookings Table -->
    <div class="overflow-hidden rounded-lg bg-white shadow-sm">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            <a href="@Url.Action("Bookings", new { sortBy = "clientName", sortDescending = (Model.SortBy == "clientName" && !Model.SortDescending), searchTerm = Model.SearchString, page = Model.Pagination?.CurrentPage ?? 1 })" class="group inline-flex">
                                Client / Contact
                                @Html.Raw(Model.SortBy == "clientName" ? (Model.SortDescending ? "↓" : "↑") : "")
                            </a>
                        </th>
                        <th scope="col" class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                           <a href="@Url.Action("Bookings", new { sortBy = "serviceType", sortDescending = (Model.SortBy == "serviceType" && !Model.SortDescending), searchTerm = Model.SearchString, page = Model.Pagination?.CurrentPage ?? 1 })" class="group inline-flex">
                                Service / Location
                                @Html.Raw(Model.SortBy == "serviceType" ? (Model.SortDescending ? "↓" : "↑") : "")
                            </a>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            <a href="@Url.Action("Bookings", new { sortBy = "bookingDate", sortDescending = (Model.SortBy == "bookingDate" && !Model.SortDescending), searchTerm = Model.SearchString, page = Model.Pagination?.CurrentPage ?? 1 })" class="group inline-flex">
                                Date / Duration
                                @Html.Raw(Model.SortBy == "bookingDate" ? (Model.SortDescending ? "↓" : "↑") : "")
                            </a>
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            <a href="@Url.Action("Bookings", new { sortBy = "status", sortDescending = (Model.SortBy == "status" && !Model.SortDescending), searchTerm = Model.SearchString, page = Model.Pagination?.CurrentPage ?? 1 })" class="group inline-flex">
                                Status
                                @Html.Raw(Model.SortBy == "status" ? (Model.SortDescending ? "↓" : "↑") : "")
                            </a>
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">Actions</th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                    @if (Model.Bookings == null || !Model.Bookings.Any())
                    {
                        <tr><td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">No bookings found.</td></tr>
                    }
                    else
                    {
                        @foreach (var booking in Model.Bookings)
                        {
                            <tr class="hover:bg-gray-50">
                                <td class="whitespace-nowrap px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">@(booking.ClientName ?? "N/A")</div>
                                    <div class="text-sm text-gray-500">@(booking.Email ?? "N/A")</div>
                                    <div class="text-sm text-gray-500 sm:hidden">@(booking.PhoneNumber ?? "N/A")</div> <!-- Show phone on small screens if email is long -->
                                </td>
                                <td class="hidden md:table-cell whitespace-nowrap px-6 py-4">
                                    <div class="text-sm text-gray-900">@(booking.ServiceType ?? "N/A")</div>
                                    <div class="text-sm text-gray-500 truncate max-w-xs" title="@(booking.Location ?? "N/A")">@(booking.Location ?? "N/A")</div>
                                </td>
                                <td class="whitespace-nowrap px-6 py-4">
                                    <div class="text-sm text-gray-900">@booking.BookingDate.ToString("MMM dd, yyyy")</div>
                                    <div class="text-sm text-gray-500">@booking.BookingDate.ToString("hh:mm tt") for @(booking.Duration ?? "N/A")</div>
                                </td>
                                <td class="whitespace-nowrap px-6 py-4">
                                    <span class="inline-flex rounded-full px-2 text-xs font-semibold leading-5 @booking.GetStatusClass()">
                                        @booking.GetStatusString()
                                    </span>
                                </td>
                                <td class="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                                    <div class="relative inline-block text-left">
                                        <button type="button"
                                                class="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                                                id="menu-button-@(booking.BookingId ?? "")" aria-expanded="false" aria-haspopup="true"
                                                onclick="toggleDropdown('@(booking.BookingId ?? "")')">
                                            Actions
                                            <svg class="-mr-1 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true"><path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" /></svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    @if (Model.Pagination != null && Model.Pagination.TotalPages > 1)
    {
        <div class="mt-6 flex flex-col sm:flex-row items-center justify-between">
            <div class="text-sm text-gray-700 mb-4 sm:mb-0 text-center sm:text-left">
                Showing <span class="font-medium">@(((Model.Pagination.CurrentPage - 1) * Model.Pagination.PageSize) + 1)</span>
                to <span class="font-medium">@Math.Min(Model.Pagination.CurrentPage * Model.Pagination.PageSize, Model.Pagination.TotalCount)</span>
                of <span class="font-medium">@Model.Pagination.TotalCount</span> bookings
            </div>
            <nav class="flex justify-center sm:justify-end" aria-label="Pagination">
                <ul class="inline-flex items-center -space-x-px">
                    <li>
                        <a href="@Url.Action("Bookings", new { page = 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })"
                           class="@(Model.Pagination.CurrentPage == 1 ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "bg-white text-gray-500 hover:bg-gray-50") inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-300 rounded-l-md">First</a>
                    </li>
                    <li>
                        <a href="@(Model.Pagination.HasPreviousPage ? Url.Action("Bookings", new { page = Model.Pagination.CurrentPage - 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending }) : "#")"
                           class="@(Model.Pagination.HasPreviousPage ? "bg-white text-gray-500 hover:bg-gray-50" : "bg-gray-100 text-gray-400 cursor-not-allowed") inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-300">Prev</a>
                    </li>
                    @{
                        int startPage = Math.Max(1, Model.Pagination.CurrentPage - 2);
                        int endPage = Math.Min(Model.Pagination.TotalPages, startPage + 4);
                        if (endPage - startPage < 4) { startPage = Math.Max(1, endPage - 4); }
                    }
                    @for (int i = startPage; i <= endPage; i++)
                    {
                        <li>
                            <a href="@Url.Action("Bookings", new { page = i, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })"
                               class="@(Model.Pagination.CurrentPage == i ? "bg-indigo-600 text-white border-indigo-600" : "bg-white text-gray-500 hover:bg-gray-50 border-gray-300") inline-flex items-center px-4 py-2 text-sm font-medium border">@i</a>
                        </li>
                    }
                    <li>
                        <a href="@(Model.Pagination.HasNextPage ? Url.Action("Bookings", new { page = Model.Pagination.CurrentPage + 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending }) : "#")"
                           class="@(Model.Pagination.HasNextPage ? "bg-white text-gray-500 hover:bg-gray-50" : "bg-gray-100 text-gray-400 cursor-not-allowed") inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-300">Next</a>
                    </li>
                    <li>
                        <a href="@Url.Action("Bookings", new { page = Model.Pagination.TotalPages, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })"
                           class="@(Model.Pagination.CurrentPage == Model.Pagination.TotalPages ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "bg-white text-gray-500 hover:bg-gray-50") inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-300 rounded-r-md">Last</a>
                    </li>
                </ul>
            </nav>
        </div>
    }
</div>

<!-- Booking Details Modal -->
<div id="bookingDetailsModal" class="fixed inset-0 z-[9999] hidden overflow-y