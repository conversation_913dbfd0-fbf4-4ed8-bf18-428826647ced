<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>@ViewData["Title"] - Care Professional Portal</title>
    <link rel="stylesheet" href="~/css/site.css" asp-append-version="true"/>
    <link rel="stylesheet" href="~/css/styles.css" asp-append-version="true">
    <link rel="stylesheet" href="~/lib/font-awesome/css/all.css"/>
    <style>
        body {
            background-color: #f3f4f6;
        }
    </style>
</head>
<body>
    <main class="min-h-screen flex-grow">
        <div class="relative isolate px-6 pt-6 lg:px-8">
            @RenderBody()
        </div>
    </main>

    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/js/htmx.min.js"></script>
    <script src="~/js/_hyperscript.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>

    @await RenderSectionAsync("Scripts", required: false)
</body>
</html>
