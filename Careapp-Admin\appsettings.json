{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ApiSettings": {"BaseUrl": "https://careappapi.intellexio.com/", "AuthEndpoint": "api/v1/auth/login", "RefreshTokenEndpoint": "api/v1/auth/refresh-token", "LogoutEndpoint": "api/v1/auth/logout", "RefreshTokenThresholdMinutes": 262800}, "JwtSettings": {"Secret": "SuperSecureKeyWithAtLeast32Characters123", "TokenCookieName": "jwt_token", "RefreshTokenCookieName": "refresh_token", "Issuer": "SuperCareApp", "Audience": "SuperCareAppUsers"}}