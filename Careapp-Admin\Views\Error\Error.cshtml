@model Northwind.ViewModels.ErrorViewModel
@{
    ViewData["Title"] = Model?.Title ?? "Error";
    Layout = "~/Views/Shared/Layouts/_LayoutsDash.cshtml";
}

<style>
    /* Main container for the error page content */
    .minimal-error-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 80vh;
        padding: 20px;
        background-color: #f4f7f6; /* Light, neutral gray background */
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
        color: #333;
        text-align: center;
    }

    .error-content-wrapper {
        max-width: 550px;
        width: 100%;
    }

    /* Animated card for SVG and 404 number */
    .error-graphic-card {
        background-color: #ffffff; /* White card */
        border-radius: 16px; /* Softer corners */
        padding: 25px 20px; /* Adjusted padding */
        box-shadow: 0 6px 18px rgba(0, 0, 0, 0.07); /* Softer, slightly larger shadow */
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-left: auto;
        margin-right: auto;
        margin-bottom: 25px;
        max-width: 220px; /* Constrain width for a neat look */
        animation: gentleFloat 6s ease-in-out infinite;
    }

    @@keyframes gentleFloat {
        0%, 100% {
            transform: translateY(0);
            box-shadow: 0 6px 18px rgba(0, 0, 0, 0.07);
        }
        50% {
            transform: translateY(-6px);
            box-shadow: 0 10px 22px rgba(0, 0, 0, 0.09);
        }
    }

    .not-found-svg {
        width: 72px; /* Size of the SVG icon */
        height: 72px;
        margin-bottom: 10px; /* Space between SVG and "404" text */
    }

    /* The "404" status code, styled for inside the card */
    .error-graphic-card .error-status-code {
        font-size: 3.5rem; /* Slightly smaller to fit well with icon */
        font-weight: 600;
        color: #2c3e50; /* Dark, slightly desaturated blue/gray */
        margin-top: 5px; /* Add a little space above if needed */
        margin-bottom: 0; /* No extra margin at bottom */
    }

    /* The main error title, e.g., "Page Not Found" */
    .error-main-title {
        font-size: 1.75rem;
        font-weight: 500;
        color: #34495e;
        margin-top: 0;
        margin-bottom: 15px;
    }

    .error-descriptive-text {
        font-size: 1rem;
        color: #7f8c8d;
        margin-bottom: 25px;
        line-height: 1.6;
    }

    .error-actions-container {
        margin-top: 20px;
        margin-bottom: 20px;
    }

    .error-actions-container a,
    .error-actions-container button {
        display: inline-block;
        padding: 12px 24px;
        margin: 8px;
        border-radius: 6px;
        text-decoration: none;
        font-size: 0.95rem;
        font-weight: 500;
        transition: background-color 0.2s ease, border-color 0.2s ease, color 0.2s ease, transform 0.2s ease;
        cursor: pointer;
        border: 1px solid transparent;
    }

    .btn-primary-action {
        background-color: #5145CD; /* UPDATED button color */
        color: #ffffff;
        border-color: #5145CD;   /* UPDATED border color */
    }

    .btn-primary-action:hover {
        background-color: #4137B8; /* UPDATED hover background color (slightly darker) */
        border-color: #4137B8;   /* UPDATED hover border color */
        transform: translateY(-2px); /* Subtle lift on hover */
    }

    .error-additional-help {
        margin-top: 30px;
        font-size: 0.875rem;
        color: #95a5a6;
    }

    @@media (max-width: 600px) {
        .error-graphic-card .error-status-code {
            font-size: 3rem;
        }
        .not-found-svg {
            width: 64px;
            height: 64px;
        }
        .error-main-title {
            font-size: 1.5rem;
        }
        .error-descriptive-text {
            font-size: 0.9rem;
        }
        .error-actions-container a,
        .error-actions-container button {
            display: block;
            width: calc(100% - 16px);
            margin-left: auto;
            margin-right: auto;
            margin-bottom: 12px;
        }
        .error-actions-container a:last-child,
        .error-actions-container button:last-child {
            margin-bottom: 0;
        }
    }
</style>

<div class="minimal-error-container">
    <div class="error-content-wrapper">

        <div class="error-graphic-card">
            <!-- Simple Ghost SVG -->
            <svg class="not-found-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
              <path d="M5 11a7 7 0 0 1 14 0v7a1.78 1.78 0 0 1 -3.1 1.4a1.65 1.65 0 0 0 -2.6 0a1.65 1.65 0 0 1 -2.6 0a1.78 1.78 0 0 1 -3.1 -1.4v-7z"
                    stroke="#7f8c8d" fill="#f1f3f5" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round" />
              <circle cx="10" cy="10.5" r="0.75" fill="#546E7A" />
              <circle cx="14" cy="10.5" r="0.75" fill="#546E7A" />
            </svg>
            <h1 class="error-status-code">404</h1>
        </div>

        <h2 class="error-main-title">
            @(Model?.Title ?? "Error")
        </h2>

        <p class="error-descriptive-text">
            @(Model?.Message ?? "An error occurred while processing your request.")
        </p>

        <div class="error-actions-container">
            <a href="@Url.Action("Index", "Dashboard")"
               class="btn-primary-action">
                Go to Dashboard
            </a>

            <button onclick="if (window.history.length > 1) { window.history.back(); } else { window.location.href = '@Url.Action("Index", "Dashboard")'; }"
                    class="rounded-lg border border-gray-500 bg-white px-4 py-2 font-medium text-gray-700 transition-colors hover:bg-gray-50">
                Go Back
            </button>
        </div>

        <p class="error-additional-help">
            If you believe this is an error, please check the URL or contact support.
        </p>
    </div>
</div>
