using System.Collections.Generic;
using Northwind.Models.Admin;

namespace Northwind.ViewModels
{
    public class AdminApprovalViewModel
    {
        public List<ApprovalRequest> ApprovalRequests { get; set; }
        public PaginationMeta Pagination { get; set; }
        public string ApprovalType { get; set; }
        public string ErrorMessage { get; set; }
        public int PendingCount { get; set; }
        public int ApprovedCount { get; set; }
        public int RejectedCount { get; set; }

        public AdminApprovalViewModel()
        {
            ApprovalRequests = new List<ApprovalRequest>();
            Pagination = new PaginationMeta();
        }
    }
}
