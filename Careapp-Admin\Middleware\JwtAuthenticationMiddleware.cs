using Northwind.Services.Auth;

namespace Northwind.Middleware
{
    public class JwtAuthenticationMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly string[] _excludedPaths =
        {
            "/auth/login",
            "/auth/register",
            "/auth/forgotpassword",
            "/auth/otpverification",
            "/auth/resetpassword",
        };

        public JwtAuthenticationMiddleware(RequestDelegate next)
        {
            _next = next;
        }

        public async Task InvokeAsync(HttpContext context, IJwtAuthService authService)
        {
            var path = context.Request.Path.Value?.ToLower();

            // Skip authentication for excluded paths
            if (path != null && IsExcludedPath(path))
            {
                await _next(context);
                return;
            }

            // Check if user is authenticated
            if (!authService.IsAuthenticated())
            {
                // Redirect to login page
                context.Response.Redirect("/auth/login");
                return;
            }

            await _next(context);
        }

        private bool IsExcludedPath(string path)
        {
            foreach (var excludedPath in _excludedPaths)
            {
                if (path.StartsWith(excludedPath))
                {
                    return true;
                }
            }

            // Also exclude static files
            if (
                path.StartsWith("/css/")
                || path.StartsWith("/js/")
                || path.StartsWith("/lib/")
                || path.StartsWith("/images/")
                || path.StartsWith("/favicon.ico")
            )
            {
                return true;
            }

            return false;
        }
    }
}
