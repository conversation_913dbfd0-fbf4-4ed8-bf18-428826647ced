using Microsoft.AspNetCore.Identity;
using Northwind.Models;
using Northwind.Services.API;
using Northwind.Services.Clients;


namespace Northwind.Extensions;

public static class ServiceExtensions
{

    public static void ConfigureIdentity(this IServiceCollection services)
    {
        // Use a simplified identity setup without EF Core
        services
            .AddIdentity<ApplicationUser, IdentityRole>(options =>
            {
                options.SignIn.RequireConfirmedAccount = false; // Simplified for mock implementation

                // Simplify password requirements for development
                options.Password.RequireDigit = false;
                options.Password.RequireLowercase = false;
                options.Password.RequireUppercase = false;
                options.Password.RequireNonAlphanumeric = false;
                options.Password.RequiredLength = 6;
            })
            .AddDefaultTokenProviders();
    }

    public static void ConfigureRailwayPattern(this IServiceCollection services)
    {
    }

    public static void ConfigureMockServices(this IServiceCollection services)
    {
        // Remove existing registration and add mock implementations for development
        var descriptor = services.FirstOrDefault(d => d.ServiceType == typeof(IClientsService));
        if (descriptor != null)
        {
            services.Remove(descriptor);
        }
        services.AddScoped<IClientsService, ClientsService>();
    }

    public static void ConfigureMediatR(this IServiceCollection services)
    {
        services.AddMediatR(configuration =>
            configuration.RegisterServicesFromAssembly(typeof(ServiceExtensions).Assembly)
        );
    }

    public static IServiceCollection ConfigureApiClient(this IServiceCollection services)
    {
        services.AddHttpClient<IApiClient, ApiClient>();
        return services;
    }
}
