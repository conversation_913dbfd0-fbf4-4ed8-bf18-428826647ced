# Careapp Admin Dashboard

<div align="center">
  <img src="wwwroot/images/logo-new.svg" alt="Careapp Admin Logo" width="300" />
  <br>
  <h3>A modern, responsive admin dashboard for healthcare management</h3>
</div>

<p align="center">
  <a href="#key-features">Key Features</a> •
  <a href="#tech-stack">Tech Stack</a> •
  <a href="#getting-started">Getting Started</a> •
  <a href="#project-structure">Project Structure</a> •
  <a href="#api-integration">API Integration</a> •
  <a href="#docker-deployment">Docker Deployment</a> •
  <a href="#configuration">Configuration</a> •
  <a href="#customization">Customization</a> •
  <a href="#contributing">Contributing</a> •
  <a href="#license">License</a>
</p>

---

## Key Features

- **Modern UI/UX Design**

  - Responsive layout built with Tailwind CSS
  - Clean, intuitive interface optimized for healthcare management
  - Collapsible sidebar for more screen space
  - Dark/light mode support

- **Healthcare Management**

  - Care professionals management with detailed profiles
  - Client management and booking system
  - Service scheduling and tracking
  - Document management for healthcare providers

- **Dashboard Analytics**

  - Real-time data visualization
  - Interactive charts and graphs for key metrics
  - Performance indicators for healthcare services
  - Customizable widgets for personalized insights

- **User Management**

  - Role-based access control with fine-grained permissions
  - Admin approval workflow for new accounts
  - User activity tracking and audit logs
  - Secure authentication with JWT

- **API-First Architecture**

  - Seamless integration with backend API services
  - Token-based authentication with automatic refresh
  - Configurable API endpoints and settings
  - Mock services for development and testing

- **Docker Support**
  - Containerized deployment with Docker and Docker Compose
  - PostgreSQL database integration
  - Health checks and dependency management
  - Environment-based configuration

## Tech Stack

### Frontend

- HTML5, CSS3, JavaScript
- Tailwind CSS for responsive styling
- Alpine.js for interactive UI components
- Chart.js for data visualization

### Backend

- ASP.NET Core 8.0 MVC
- C# programming language
- MediatR for CQRS pattern implementation
- JWT authentication with token refresh

### Infrastructure

- Docker and Docker Compose for containerization
- PostgreSQL for data persistence
- RESTful API integration
- Environment-based configuration

## Getting Started

### Prerequisites

- .NET 8.0 SDK or later
- Docker and Docker Compose (for containerized deployment)
- Node.js and npm (for Tailwind CSS development)
- Visual Studio 2022, VS Code, or any preferred IDE

### Local Development Setup

1. **Clone the repository**

   ```bash
   git clone https://github.com/yourusername/careapp-admin.git
   cd careapp-admin
   ```

2. **Restore dependencies**

   ```bash
   dotnet restore
   ```

3. **Run the application**

   ```bash
   dotnet run
   ```

4. **Access the application**

   Open your browser and navigate to `https://localhost:5001` or `http://localhost:5000`

### Docker Deployment

1. **Create a `.env` file in the project root with the following variables:**

   ```
   JWT_SECRET=YourSuperSecureKeyWithAtLeast32Characters123!@#
   POSTGRES_USER=careappuser
   POSTGRES_PASSWORD=yoursecurepassword
   POSTGRES_DB=careappdb
   ```

2. **Build and start the containers:**

   ```bash
   docker-compose up -d
   ```

3. **Access the application:**

   Open your browser and navigate to `http://localhost:9753`

## Project Structure

```
Careapp-Admin/
├── Configuration/           # Application configuration classes
│   ├── ApiSettings.cs       # API configuration settings
│   └── JwtSettings.cs       # JWT authentication settings
├── Controllers/             # MVC Controllers
│   ├── DashboardController.cs
│   ├── AuthController.cs
│   └── ErrorController.cs
├── Extensions/              # Extension methods
│   ├── ServiceExtensions.cs
│   └── ServiceCollectionExtensions.cs
├── Middleware/              # Custom middleware components
│   ├── JwtAuthenticationMiddleware.cs
│   └── TokenRefreshMiddleware.cs
├── Models/                  # Data models
│   ├── Professionals/       # Care professional models
│   ├── Clients/             # Client models
│   └── Services/            # Service models
├── Services/                # Business logic and services
│   ├── API/                 # API integration services
│   ├── Auth/                # Authentication services
│   ├── Mock/                # Mock data providers for development
│   └── Http/                # HTTP client services
├── ViewModels/              # View-specific models
├── Views/                   # Razor views
│   ├── Dashboard/           # Dashboard views
│   ├── Auth/                # Authentication views
│   └── Shared/              # Shared layouts and partials
├── wwwroot/                 # Static files (CSS, JS, images)
├── Dockerfile               # Docker configuration
├── docker-compose.yml       # Docker Compose configuration
├── Program.cs               # Application entry point
└── appsettings.json         # Application settings
```

## API Integration

The application is designed to work with external APIs for data access. The API integration is configured in the following files:

- **appsettings.json**: Contains API endpoint configuration
- **ApiSettings.cs**: Defines the API configuration model
- **ServiceCollectionExtensions.cs**: Configures API services and HTTP clients
- **ApiService.cs**: Implements API communication

### API Configuration

Configure API settings in `appsettings.json`:

```json
"ApiSettings": {
  "BaseUrl": "https://careappapi.intellexio.com/",
  "AuthEndpoint": "api/v1/auth/login",
  "RefreshTokenEndpoint": "api/v1/auth/refresh-token",
  "LogoutEndpoint": "api/v1/auth/logout",
  "RefreshTokenThresholdMinutes": 30
}
```

### Authentication Flow

The application uses JWT authentication with token refresh:

1. User logs in via the AuthController
2. JWT token is stored in a cookie
3. TokenRefreshMiddleware automatically refreshes tokens before they expire
4. JwtAuthenticationMiddleware validates tokens for protected routes

## Docker Deployment

The application includes Docker support for containerized deployment:

### Dockerfile

The `Dockerfile` uses a multi-stage build process:

1. Base image: ASP.NET Core 8.0 runtime
2. Build stage: Restores dependencies and builds the application
3. Publish stage: Creates a production-ready build
4. Final stage: Copies the published application to the runtime image

### Docker Compose

The `docker-compose.yml` file defines the following services:

- **careapp-admin**: The main application container

  - Exposes port 9753
  - Configures environment variables for API and JWT settings
  - Depends on the PostgreSQL service
  - Includes health checks

- **postgres**: PostgreSQL database container
  - Uses PostgreSQL 15
  - Configures database credentials from environment variables
  - Persists data using a named volume
  - Includes health checks

### Environment Variables

Create a `.env` file in the same directory as the `docker-compose.yml` file with the following variables:

```
JWT_SECRET=YourSuperSecureKeyWithAtLeast32Characters123!@#
POSTGRES_USER=careappuser
POSTGRES_PASSWORD=yoursecurepassword
POSTGRES_DB=careappdb
```

## Configuration

### Application Settings

The application is configured through the `appsettings.json` file:

```json
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ApiSettings": {
    "BaseUrl": "https://careappapi.intellexio.com/",
    "AuthEndpoint": "api/v1/auth/login",
    "RefreshTokenEndpoint": "api/v1/auth/refresh-token",
    "LogoutEndpoint": "api/v1/auth/logout",
    "RefreshTokenThresholdMinutes": 30
  },
  "JwtSettings": {
    "Secret": "SuperSecureKeyWithAtLeast32Characters123",
    "TokenCookieName": "jwt_token",
    "RefreshTokenCookieName": "refresh_token",
    "Issuer": "SuperCareApp",
    "Audience": "SuperCareAppUsers"
  }
}
```

### Environment-Specific Configuration

The application supports different configurations for development and production environments:

- **Development**: Uses `appsettings.Development.json`
- **Production**: Uses `appsettings.json` and environment variables

## Customization

### Theme Customization

The application uses Tailwind CSS, making it easy to customize the theme:

1. **Colors**: Edit the `tailwind.config.js` file to change the color scheme
2. **Layout**: Modify the layout files in `Views/Shared/Layouts/`
3. **Components**: Create or modify partial views in `Views/Shared/`

### Adding New Features

1. **Create a new controller** in the `Controllers` folder
2. **Create view models** in the `ViewModels` folder
3. **Create views** in the `Views` folder
4. **Add navigation links** in the sidebar (`Views/Shared/Layouts/_LayoutsDash.cshtml`)
5. **Register any new services** in `Program.cs`

## Contributing

We welcome contributions to improve the Careapp Admin Dashboard! Here's how you can contribute:

1. **Fork the repository**
2. **Create a feature branch**:
   ```bash
   git checkout -b feature/amazing-feature
   ```
3. **Commit your changes**:
   ```bash
   git commit -m 'Add some amazing feature'
   ```
4. **Push to the branch**:
   ```bash
   git push origin feature/amazing-feature
   ```
5. **Open a Pull Request**

Please make sure your code follows the existing style and includes appropriate tests.
