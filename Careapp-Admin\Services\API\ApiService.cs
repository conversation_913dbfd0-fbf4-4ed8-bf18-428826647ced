using System;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using Northwind.Configuration;

namespace Northwind.Services.Api
{
    public class ApiService : IApiService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ApiSettings _apiSettings;
        private const string API_CLIENT_NAME = "ApiClient";

        public ApiService(IHttpClientFactory httpClientFactory, IOptions<ApiSettings> apiSettings)
        {
            _httpClientFactory = httpClientFactory;
            _apiSettings = apiSettings.Value;
        }

        public async Task<T> GetAsync<T>(string endpoint)
        {
            var client = _httpClientFactory.CreateClient(API_CLIENT_NAME);
            var response = await client.GetAsync(endpoint);

            // This will throw HttpRequestException with the appropriate status code if the request fails
            response.EnsureSuccessStatusCode();

            return await response.Content.ReadFromJsonAsync<T>();
        }

        public async Task<T> GetWithHeadersAsync<T>(string endpoint, Dictionary<string, string> headers)
        {
            try
            {
                var client = _httpClientFactory.CreateClient(API_CLIENT_NAME);

                // Add custom headers
                if (headers != null)
                {
                    foreach (var header in headers)
                    {
                        client.DefaultRequestHeaders.Add(header.Key, header.Value);
                    }
                }

                // Log the request details
                Console.WriteLine($"Making GET request with custom headers to: {client.BaseAddress}{endpoint}");

                // Log the request headers
                Console.WriteLine("Request headers:");
                foreach (var header in client.DefaultRequestHeaders)
                {
                    Console.WriteLine($"{header.Key}: {string.Join(", ", header.Value)}");
                }

                var response = await client.GetAsync(endpoint);

                // Log the response
                Console.WriteLine($"Response status code: {response.StatusCode}");
                var responseContent = await response.Content.ReadAsStringAsync();
                Console.WriteLine($"Response content: {responseContent}");

                // This will throw HttpRequestException with the appropriate status code if the request fails
                response.EnsureSuccessStatusCode();

                // Clear the custom headers after the request
                if (headers != null)
                {
                    foreach (var header in headers)
                    {
                        client.DefaultRequestHeaders.Remove(header.Key);
                    }
                }

                return JsonSerializer.Deserialize<T>(responseContent, new JsonSerializerOptions
                {
                    PropertyNameCaseInsensitive = true
                });
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Exception in GetWithHeadersAsync: {ex.Message}");
                Console.WriteLine($"Stack trace: {ex.StackTrace}");
                throw;
            }
        }

        public async Task<TResponse> PostAsync<TRequest, TResponse>(string endpoint, TRequest data)
        {
            var client = _httpClientFactory.CreateClient(API_CLIENT_NAME);

            var content = new StringContent(
                JsonSerializer.Serialize(data),
                Encoding.UTF8,
                "application/json");

            var response = await client.PostAsync(endpoint, content);

            response.EnsureSuccessStatusCode();

            return await response.Content.ReadFromJsonAsync<TResponse>();
        }

        public async Task<TResponse> PutAsync<TRequest, TResponse>(string endpoint, TRequest data)
        {
            var client = _httpClientFactory.CreateClient(API_CLIENT_NAME);

            var content = new StringContent(
                JsonSerializer.Serialize(data),
                Encoding.UTF8,
                "application/json");

            var response = await client.PutAsync(endpoint, content);

            response.EnsureSuccessStatusCode();

            return await response.Content.ReadFromJsonAsync<TResponse>();
        }

        public async Task<bool> DeleteAsync(string endpoint)
        {
            var client = _httpClientFactory.CreateClient(API_CLIENT_NAME);
            var response = await client.DeleteAsync(endpoint);

            return response.IsSuccessStatusCode;
        }

        public async Task<TResponse> PostEmptyAsync<TResponse>(string endpoint)
        {
            var client = _httpClientFactory.CreateClient(API_CLIENT_NAME);

            // Log the request details
            Console.WriteLine($"Making POST request with empty body to: {client.BaseAddress}{endpoint}");

            // Create an empty content
            var content = new StringContent("{}", Encoding.UTF8, "application/json");

            // Log the request headers
            Console.WriteLine("Request headers:");
            foreach (var header in client.DefaultRequestHeaders)
            {
                Console.WriteLine($"{header.Key}: {string.Join(", ", header.Value)}");
            }

            var response = await client.PostAsync(endpoint, content);

            // Log the response
            Console.WriteLine($"Response status code: {response.StatusCode}");
            var responseContent = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"Response content: {responseContent}");

            response.EnsureSuccessStatusCode();

            return JsonSerializer.Deserialize<TResponse>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }

        public async Task<TResponse> PostEmptyStringAsync<TResponse>(string endpoint)
        {
            var client = _httpClientFactory.CreateClient(API_CLIENT_NAME);

            // Log the request details
            Console.WriteLine($"Making POST request with empty string body to: {client.BaseAddress}{endpoint}");

            // Create an empty string content
            var content = new StringContent("", Encoding.UTF8, "application/json");

            // Log the request headers
            Console.WriteLine("Request headers:");
            foreach (var header in client.DefaultRequestHeaders)
            {
                Console.WriteLine($"{header.Key}: {string.Join(", ", header.Value)}");
            }

            var response = await client.PostAsync(endpoint, content);

            // Log the response
            Console.WriteLine($"Response status code: {response.StatusCode}");
            var responseContent = await response.Content.ReadAsStringAsync();
            Console.WriteLine($"Response content: {responseContent}");

            response.EnsureSuccessStatusCode();

            return JsonSerializer.Deserialize<TResponse>(responseContent, new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            });
        }
    }
}
