@model Northwind.ViewModels.ServicesViewModel
@{
    ViewData["Title"] = "Services";
    Layout = "~/Views/Shared/Layouts/_LayoutsDash.cshtml";
}
@* For HttpUtility.JavaScriptStringEncode if you choose to use it for more robust string escaping *@
@* @using System.Web *@

<partial name="_SuccessNotification" />
<partial name="_ErrorNotification" />

<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-2xl font-bold text-gray-900">Care Services</h1>
        <p class="mt-1 text-sm text-gray-600">Manage care service categories</p>
    </div>

    <!-- Error Message (if any) -->
    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="mb-6 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg shadow-md">
            <p>@Model.ErrorMessage</p>
        </div>
    }

    <!-- Filter -->
    <div class="mb-6 flex flex-col md:flex-row justify-between items-center bg-white p-4 rounded-lg shadow-md">
        <form method="get" action="@Url.Action("Services", "Dashboard")" class="w-full md:w-1/2">
            <div class="flex items-center">
                <div class="flex items-center mr-4">
                    <input id="includeInactive" name="includeInactive" type="checkbox" @(Model.IncludeInactive ? "checked" : "")
                        class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                    <label for="includeInactive" class="ml-2 block text-sm text-gray-800">Include Inactive</label>
                </div>
                <button type="submit" class="rounded-lg bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 transition-colors duration-200">
                    Apply Filter
                </button>
            </div>
        </form>
        <div class="mt-4 md:mt-0">
            <button onclick="openAddCategoryModal()" class="inline-flex items-center rounded-lg bg-green-600 px-4 py-2 text-white hover:bg-green-700 shadow-sm transition-all duration-200 hover:shadow">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add New Category
            </button>
        </div>
    </div>

    <!-- Dropdown Menus (Positioned outside table to avoid clipping) -->
    @if (Model.CareCategories != null)
    {
        @for (int j = 0; j < Model.CareCategories.Count; j++)
        {
            var category = Model.CareCategories[j];
            <div class="fixed z-50 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden"
                 role="menu"
                 aria-orientation="vertical"
                 aria-labelledby="<EMAIL>"
                 id="<EMAIL>"
                 style="z-index: 9999;">
                <div class="py-1" role="none">
                    <a href="#" onclick="editCategory('@category.Id', '@(category.Name)', '@(category.Description ?? string.Empty)')" class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-900" role="menuitem">
                        <svg class="mr-3 h-5 w-5 text-indigo-600 group-hover:text-indigo-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Edit Category
                    </a>
                    @if (category.IsActive)
                    {
                        <a href="#" onclick="deactivateCategory('@category.Id')" class="group flex items-center px-4 py-2 text-sm text-red-700 hover:bg-red-50 hover:text-red-900" role="menuitem">
                            <svg class="mr-3 h-5 w-5 text-red-600 group-hover:text-red-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Deactivate
                        </a>
                    }
                    else
                    {
                        <a href="#" onclick="activateCategory('@category.Id')" class="group flex items-center px-4 py-2 text-sm text-green-700 hover:bg-green-50 hover:text-green-900" role="menuitem">
                            <svg class="mr-3 h-5 w-5 text-green-600 group-hover:text-green-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            Activate
                        </a>
                    }
                </div>
                <div class="py-1" role="none">

                </div>
                <div class="py-1" role="none">
                    <a href="#" onclick="openDeleteModal('@category.Id', '@category.Name')" class="group flex items-center px-4 py-2 text-sm text-red-700 hover:bg-red-50 hover:text-red-900" role="menuitem">
                        <svg class="mr-3 h-5 w-5 text-red-600 group-hover:text-red-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                        Delete Category
                    </a>
                </div>
            </div>
        }
    }

    <!-- Care Categories Table -->
    <div class="overflow-x-auto rounded-lg bg-white shadow-md">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Name
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Description
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Status
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Created At
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                        Last Updated
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-200 bg-white">
                @if (Model.CareCategories != null && Model.CareCategories.Any())
                {
                    @for (int i = 0; i < Model.CareCategories.Count; i++)
                    {
                        var category = Model.CareCategories[i];
                        <tr class="hover:bg-gray-50">
                            <td class="whitespace-nowrap px-6 py-4 text-sm font-medium text-gray-900">
                                @category.Name
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                @category.Description
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm">
                                @if (category.IsActive)
                                {
                                    <span class="inline-flex rounded-full bg-green-100 px-2 text-xs font-semibold leading-5 text-green-800">Active</span>
                                }
                                else
                                {
                                    <span class="inline-flex rounded-full bg-red-100 px-2 text-xs font-semibold leading-5 text-red-800">Inactive</span>
                                }
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                                @category.CreatedAt.ToString("MMM dd, yyyy")
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-sm text-gray-500">
                                @(category.UpdatedAt.HasValue ? category.UpdatedAt.Value.ToString("MMM dd, yyyy") : "-")
                            </td>
                            <td class="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                                <div class="relative inline-block text-left">
                                    <button type="button"
                                            class="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                                            id="<EMAIL>"
                                            aria-expanded="false"
                                            aria-haspopup="true"
                                            onclick="toggleDropdown('@category.Id')">
                                        Actions
                                        <svg class="-mr-1 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    }
                }
                else
                {
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-sm text-gray-500">
                            No care categories found.
                        </td>
                    </tr>
                }
            </tbody>
        </table>
    </div>

    <!-- Add Category Modal -->
    <div id="addCategoryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 items-center justify-center z-50 hidden"
        style="display: none;">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Add New Category</h3>
                <button type="button" onclick="closeAddCategoryModal()" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <form id="addCategoryForm" method="post" action="@Url.Action("AddCategory", "Dashboard")" class="space-y-4">
                <input type="hidden" name="includeInactive" value="@Model.IncludeInactive.ToString().ToLower()" />

                <div>
                    <label for="name" class="block text-sm font-medium text-gray-700">Category Name</label>
                    <input type="text" id="name" name="name" required
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Enter category name">
                </div>

                <div>
                    <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
                    <textarea id="description" name="description" rows="3"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Enter category description"></textarea>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeAddCategoryModal()"
                        class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit"
                        class="inline-flex justify-center rounded-md border border-transparent bg-green-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-green-700">
                        Add Category
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Edit Category Modal -->
    <div id="editCategoryModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 items-center justify-center z-50 hidden"
        style="display: none;">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Edit Category</h3>
                <button type="button" onclick="closeEditCategoryModal()" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <form id="editCategoryForm" method="post" action="@Url.Action("UpdateCategory", "Dashboard")" class="space-y-4">
                <input type="hidden" id="editCategoryId" name="categoryId" />
                <input type="hidden" name="includeInactive" value="@Model.IncludeInactive.ToString().ToLower()" />

                <div>
                    <label for="editName" class="block text-sm font-medium text-gray-700">Category Name</label>
                    <input type="text" id="editName" name="name" required
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Enter category name">
                </div>

                <div>
                    <label for="editDescription" class="block text-sm font-medium text-gray-700">Description</label>
                    <textarea id="editDescription" name="description" rows="3"
                        class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                        placeholder="Enter category description"></textarea>
                </div>

                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="closeEditCategoryModal()"
                        class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit"
                        class="inline-flex justify-center rounded-md border border-transparent bg-yellow-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-blue-700">
                        Update Category
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div id="deleteConfirmationModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 items-center justify-center z-50 hidden"
        style="display: none;">
        <div class="bg-white rounded-lg shadow-lg p-6 w-full max-w-md">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-medium text-gray-900">Confirm Delete</h3>
                <button type="button" onclick="closeDeleteModal()" class="text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            <div class="mb-4">
                <p class="text-sm text-gray-700">
                    Are you sure you want to delete the category <span id="categoryNameToDelete" class="font-semibold"></span>?
                    This action cannot be undone.
                </p>
            </div>
            <form method="post" action="@Url.Action("DeleteCategory", "Dashboard")">
                <input type="hidden" id="deleteCategoryId" name="categoryId" />
                <input type="hidden" name="includeInactive" value="@Model.IncludeInactive.ToString().ToLower()" />

                <div class="flex justify-end space-x-3">
                    <button type="button" onclick="closeDeleteModal()"
                        class="inline-flex justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50">
                        Cancel
                    </button>
                    <button type="submit"
                        class="inline-flex justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-red-700">
                        Delete
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Function to open the add category modal
        function openAddCategoryModal() {
            document.getElementById('addCategoryModal').style.display = 'flex';
        }

        // Function to close the add category modal
        function closeAddCategoryModal() {
            document.getElementById('addCategoryModal').style.display = 'none';
            document.getElementById('addCategoryForm').reset();
        }

        // Function to open the edit category modal
        function openEditCategoryModal(categoryId, currentName, currentDescription) {
            document.getElementById('editCategoryId').value = categoryId;
            document.getElementById('editName').value = currentName;
            document.getElementById('editDescription').value = currentDescription;
            document.getElementById('editCategoryModal').style.display = 'flex';
        }

        // Function to close the edit category modal
        function closeEditCategoryModal() {
            document.getElementById('editCategoryModal').style.display = 'none';
            document.getElementById('editCategoryForm').reset(); 
        }

        // Function to open the delete confirmation modal
        function openDeleteModal(categoryId, categoryName) {
            document.getElementById('deleteCategoryId').value = categoryId;
            document.getElementById('categoryNameToDelete').textContent = categoryName;
            document.getElementById('deleteConfirmationModal').style.display = 'flex';
        }

        // Function to close the delete confirmation modal
        function closeDeleteModal() {
            document.getElementById('deleteConfirmationModal').style.display = 'none';
        }

        // Toggle dropdown function with proper positioning
        function toggleDropdown(categoryId) {
            const dropdown = document.getElementById(`dropdown-${categoryId}`);
            const button = document.getElementById(`menu-button-${categoryId}`);

            // Close all other dropdowns first
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => {
                if (dd.id !== `dropdown-${categoryId}`) {
                    dd.classList.add('hidden');
                    const otherButtonId = `menu-button-${dd.id.replace('dropdown-', '')}`;
                    const otherButton = document.getElementById(otherButtonId);
                    if (otherButton) {
                        otherButton.setAttribute('aria-expanded', 'false');
                    }
                }
            });

            // Toggle current dropdown
            if (dropdown.classList.contains('hidden')) { // If it's hidden, we want to show and position it
                const buttonRect = button.getBoundingClientRect();

                // IMPORTANT: Make the dropdown visible (but not yet positioned) to measure its actual dimensions
                dropdown.classList.remove('hidden'); 
                const dropdownWidth = dropdown.offsetWidth;
                // const dropdownHeight = dropdown.offsetHeight; // If vertical positioning adjustments are needed

                let newTop = buttonRect.bottom + 2; // 2px spacing below button
                // Default: Align right edge of dropdown with right edge of button
                let newLeft = buttonRect.right - dropdownWidth; 

                const viewportWidth = window.innerWidth;
                const viewportPadding = 10; // Adjust this for more/less space from viewport edges

                // Adjust if dropdown overflows right edge of viewport
                if (newLeft + dropdownWidth > viewportWidth - viewportPadding) {
                    newLeft = viewportWidth - dropdownWidth - viewportPadding;
                }

                // Adjust if dropdown overflows left edge of viewport
                // (This can happen if the button is far left, or viewport is very narrow and right-alignment pushed it too far left)
                if (newLeft < viewportPadding) {
                    newLeft = viewportPadding;
                }
                
                // The dropdown's style attribute already has `position: fixed; z-index: 9999;`
                // So we only need to set top and left.
                dropdown.style.top = newTop + 'px';
                dropdown.style.left = newLeft + 'px';
                
                button.setAttribute('aria-expanded', 'true');

            } else { // If it's visible, we want to hide it
                dropdown.classList.add('hidden');
                button.setAttribute('aria-expanded', 'false');
            }
        }

        // Close dropdowns when clicking outside
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Services page loaded successfully');

            document.addEventListener('click', function(event) {
                const dropdowns = document.querySelectorAll('[id^="dropdown-"]');
                dropdowns.forEach(dropdown => {
                    const buttonId = `menu-button-${dropdown.id.replace('dropdown-', '')}`;
                    const button = document.getElementById(buttonId);
                    if (button && !button.contains(event.target) && !dropdown.contains(event.target)) {
                        dropdown.classList.add('hidden');
                        button.setAttribute('aria-expanded', 'false');
                    }
                });

                const modals = document.querySelectorAll('.fixed.inset-0.bg-gray-600.bg-opacity-50');
                modals.forEach(modal => {
                    if (event.target === modal) {
                        if (modal.id === 'addCategoryModal') closeAddCategoryModal();
                        if (modal.id === 'editCategoryModal') closeEditCategoryModal();
                        if (modal.id === 'deleteConfirmationModal') closeDeleteModal();
                    }
                });
            });
        });

        // Edit category function (now calls openEditCategoryModal)
        function editCategory(categoryId, name, description) {
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => {
                 dd.classList.add('hidden');
                 const button = document.querySelector(`[id="menu-button-${dd.id.replace('dropdown-', '')}"]`);
                 if(button) button.setAttribute('aria-expanded', 'false');
            });
            openEditCategoryModal(categoryId, name, description);
        }

        // Activate category function
        function activateCategory(categoryId) {
            console.log('Activating category:', categoryId);
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => {
                 dd.classList.add('hidden');
                 const button = document.querySelector(`[id="menu-button-${dd.id.replace('dropdown-', '')}"]`);
                 if(button) button.setAttribute('aria-expanded', 'false');
            });

            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '@Url.Action("ActivateCategory", "Dashboard")';
            form.style.display = 'none';

            const categoryInput = document.createElement('input');
            categoryInput.type = 'hidden';
            categoryInput.name = 'categoryId';
            categoryInput.value = categoryId;
            form.appendChild(categoryInput);

            const includeInactiveInput = document.createElement('input');
            includeInactiveInput.type = 'hidden';
            includeInactiveInput.name = 'includeInactive';
            includeInactiveInput.value = '@Model.IncludeInactive.ToString().ToLower()';
            form.appendChild(includeInactiveInput);

            document.body.appendChild(form);
            form.submit();
        }

        // Deactivate category function
        function deactivateCategory(categoryId) {
            console.log('Deactivating category:', categoryId);
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => {
                 dd.classList.add('hidden');
                 const button = document.querySelector(`[id="menu-button-${dd.id.replace('dropdown-', '')}"]`);
                 if(button) button.setAttribute('aria-expanded', 'false');
            });

            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '@Url.Action("DeactivateCategory", "Dashboard")';
            form.style.display = 'none';

            const categoryInput = document.createElement('input');
            categoryInput.type = 'hidden';
            categoryInput.name = 'categoryId';
            categoryInput.value = categoryId;
            form.appendChild(categoryInput);

            const includeInactiveInput = document.createElement('input');
            includeInactiveInput.type = 'hidden';
            includeInactiveInput.name = 'includeInactive';
            includeInactiveInput.value = '@Model.IncludeInactive.ToString().ToLower()';
            form.appendChild(includeInactiveInput);

            document.body.appendChild(form);
            form.submit();
        }
    </script>
}