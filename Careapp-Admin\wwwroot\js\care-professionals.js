/**
 * Care Professionals page JavaScript
 */
document.addEventListener('DOMContentLoaded', function () {
    // Initialize toast notifications
    initializeToasts();

    // Add HTMX event listeners
    setupHtmxListeners();

    // Setup dropdown positioning
    setupDropdownPositioning();
});

/**
 * Initialize toast notifications
 */
function initializeToasts() {
    // Listen for successful operations
    document.body.addEventListener('care-professional-updated', function (event) {
        showToast('success', 'Care professional updated successfully');
    });

    document.body.addEventListener('care-professional-activated', function (event) {
        showToast('success', 'Care professional activated successfully');
    });

    document.body.addEventListener('care-professional-deactivated', function (event) {
        showToast('success', 'Care professional deactivated successfully');
    });

    document.body.addEventListener('care-professional-deleted', function (event) {
        showToast('success', 'Care professional deleted successfully');
    });

    // Listen for errors
    document.body.addEventListener('care-professional-error', function (event) {
        const message = event.detail ? event.detail.message : 'An error occurred';
        showToast('error', message);
    });
}

/**
 * Setup HTMX event listeners
 */
function setupHtmxListeners() {
    // Listen for HTMX after swap event to close dropdowns
    document.body.addEventListener('htmx:afterSwap', function (event) {
        // Find all open dropdowns and close them
        const openDropdowns = document.querySelectorAll('[x-data]');
        openDropdowns.forEach(dropdown => {
            if (dropdown.__x) {
                dropdown.__x.$data.open = false;
            }
        });
    });

    // Listen for HTMX before request event to show loading state
    document.body.addEventListener('htmx:beforeRequest', function (event) {
        const button = event.detail.elt;
        if (button.tagName === 'BUTTON' || button.tagName === 'A') {
            // Store original text
            button.dataset.originalText = button.innerHTML;

            // Replace with loading indicator
            button.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Processing...';
            button.disabled = true;
        }
    });

    // Listen for HTMX after request event to restore button state
    document.body.addEventListener('htmx:afterRequest', function (event) {
        const button = event.detail.elt;
        if (button.tagName === 'BUTTON' || button.tagName === 'A') {
            // Restore original text
            if (button.dataset.originalText) {
                button.innerHTML = button.dataset.originalText;
                button.disabled = false;
            }
        }
    });
}

/**
 * Setup dropdown positioning
 */
function setupDropdownPositioning() {
    // Fix table overflow issues
    const tables = document.querySelectorAll('table');
    tables.forEach(table => {
        table.style.overflow = 'visible';
    });

    // Fix table cell overflow issues
    const tableCells = document.querySelectorAll('td');
    tableCells.forEach(cell => {
        cell.style.overflow = 'visible';
    });

    // Fix table container overflow issues
    const tableContainers = document.querySelectorAll('.overflow-x-auto, .overflow-y-auto, .overflow-auto');
    tableContainers.forEach(container => {
        container.style.overflow = 'visible';
    });

    // Get all dropdown toggle buttons
    const dropdownButtons = document.querySelectorAll('[x-on\\:click="open = !open"]');

    // Add click event listeners to position dropdowns
    dropdownButtons.forEach(button => {
        button.addEventListener('click', function (event) {
            // Get the dropdown menu element
            const container = button.closest('.dropdown-container');
            const dropdown = container.querySelector('.dropdown-menu');

            if (!dropdown) return;

            // Get button position
            const buttonRect = button.getBoundingClientRect();
            const containerRect = container.getBoundingClientRect();

            // Position dropdown relative to the viewport
            dropdown.style.position = 'fixed';
            dropdown.style.top = `${buttonRect.bottom + 5}px`;
            dropdown.style.right = `${window.innerWidth - buttonRect.right}px`;
            dropdown.style.zIndex = '9999';

            // Ensure dropdown is visible within viewport
            setTimeout(() => {
                const dropdownRect = dropdown.getBoundingClientRect();

                // Check if dropdown extends beyond bottom of viewport
                if (dropdownRect.bottom > window.innerHeight) {
                    // Position above button instead
                    dropdown.style.top = `${buttonRect.top - dropdownRect.height - 5}px`;
                }

                // Check if dropdown extends beyond left edge of viewport
                if (dropdownRect.left < 0) {
                    dropdown.style.right = 'auto';
                    dropdown.style.left = `${buttonRect.left}px`;
                }

                // Check if dropdown extends beyond right edge of viewport
                if (dropdownRect.right > window.innerWidth) {
                    dropdown.style.right = '10px';
                    dropdown.style.left = 'auto';
                }
            }, 10);
        });
    });

    // Close dropdowns when scrolling
    document.addEventListener('scroll', function () {
        // Find all Alpine.js components with open dropdowns
        document.querySelectorAll('[x-data]').forEach(component => {
            if (component.__x && component.__x.$data.open === true) {
                component.__x.$data.open = false;
            }
        });
    }, { passive: true });
}

/**
 * Show a toast notification
 * @param {string} type - The type of toast (success, error, warning, info)
 * @param {string} message - The message to display
 */
function showToast(type, message) {
    // Check if the toast notification function exists
    if (typeof window.showToastNotification === 'function') {
        window.showToastNotification(type, message);
    } else {
        console.log(`Toast (${type}): ${message}`);
    }
}
