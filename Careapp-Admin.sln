﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.10.34916.146
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Careapp-Admin", "Careapp-Admin\Careapp-Admin.csproj", "{1DB035D0-3FBE-0EF0-5386-54B3AC7E25DB}"
EndProject
Project("{E53339B2-1760-4266-BCC7-CA923CBCF16C}") = "docker-compose", "docker-compose.dcproj", "{81DDED9D-158B-E303-5F62-77A2896D2A5A}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{1DB035D0-3FBE-0EF0-5386-54B3AC7E25DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1DB035D0-3FBE-0EF0-5386-54B3AC7E25DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1DB035D0-3FBE-0EF0-5386-54B3AC7E25DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1DB035D0-3FBE-0EF0-5386-54B3AC7E25DB}.Release|Any CPU.Build.0 = Release|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{81DDED9D-158B-E303-5F62-77A2896D2A5A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {B98362C9-0BB1-4138-A600-158137C2226A}
	EndGlobalSection
EndGlobal
