using System.Text.Json.Serialization;

namespace Northwind.Models.Professionals
{
    public class RejectProviderRequest
    {
        /// <summary>
        /// The ID of the approval request to reject
        /// </summary>
        [JsonPropertyName("providerId")]
        public string ProviderId { get; set; }

        /// <summary>
        /// Required reason for rejection (visible to the provider)
        /// </summary>
        [JsonPropertyName("rejectionReason")]
        public string RejectionReason { get; set; }

        /// <summary>
        /// Optional additional notes (for internal use only)
        /// </summary>
        [JsonPropertyName("notes")]
        public string Notes { get; set; }

        /// <summary>
        /// Current page number for redirecting back to the list
        /// </summary>
        [JsonPropertyName("page")]
        public int Page { get; set; } = 1;

        /// <summary>
        /// Current search term for redirecting back to the list
        /// </summary>
        [JsonPropertyName("searchTerm")]
        public string SearchTerm { get; set; } = "";

        /// <summary>
        /// Current sort field for redirecting back to the list
        /// </summary>
        [JsonPropertyName("sortBy")]
        public string SortBy { get; set; } = "createdAt";

        /// <summary>
        /// Current sort direction for redirecting back to the list
        /// </summary>
        [JsonPropertyName("sortDescending")]
        public bool SortDescending { get; set; } = true;
    }
}
