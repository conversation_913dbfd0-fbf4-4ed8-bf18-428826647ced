@model Northwind.ViewModels.ClientsViewModel
@{
    ViewData["Title"] = "Clients Management";
    Layout = "Layouts/_LayoutsDash";
}

<partial name="_SuccessNotification" />
<partial name="_ErrorNotification" />

<div class="p-6">
    <!-- Enhanced Responsive Header Section -->
    <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 class="text-2xl font-bold text-gray-800">Clients Management</h1>
        <div class="mt-4 w-full sm:w-auto sm:mt-0">
            <form asp-controller="Dashboard" asp-action="Clients" method="get" class="flex flex-col sm:flex-row items-center gap-2">
                <div class="relative w-full sm:w-auto">
                    <input type="text" name="searchTerm" value="@Model.SearchString"
                        class="w-full sm:w-64 rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                        placeholder="Search clients..."
                        aria-label="Search clients">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20"
                            fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd"
                                d="M8 4a4 4 0 100 8a4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                </div>
                <!-- Hidden fields to preserve sorting parameters -->
                <input type="hidden" name="sortBy" value="@Model.SortBy" />
                <input type="hidden" name="sortDescending" value="@Model.SortDescending.ToString().ToLower()" />
                <button type="submit"
                    class="w-full sm:w-auto rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    aria-label="Submit search">
                    Search
                </button>
            </form>
        </div>
    </div>

    <!-- Error Messages -->
    @if (!string.IsNullOrEmpty(Model.ErrorMessage))
    {
        <div class="mb-6 rounded-md bg-red-50 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Error</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p>@Model.ErrorMessage</p>
                    </div>
                </div>
            </div>
        </div>
    }
    @if (TempData["ErrorMessage"] != null)
    {
        <div class="mb-6 rounded-md bg-red-50 p-4">
            <div class="flex">
                <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                    </svg>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Error</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p>@TempData["ErrorMessage"]</p>
                    </div>
                </div>
            </div>
        </div>
    }

    <!-- Stats Cards -->
    <div class="mb-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <!-- Total Clients Card -->
        <div class="overflow-hidden rounded-lg bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m3 5.197H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="font-semibold text-gray-500">Total Clients</h2>
                    <p class="text-2xl font-bold text-gray-800">@(Model.Pagination?.TotalCount ?? 0)</p>
                </div>
            </div>
        </div>

        <!-- Clients with Phone Card -->
        <div class="overflow-hidden rounded-lg bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-emerald-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-emerald-600" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="font-semibold text-gray-500">With Phone</h2>
                    <p class="text-2xl font-bold text-gray-800">@(Model.Clients?.Count(c => !string.IsNullOrEmpty(c.PhoneNumber)) ?? 0)</p>
                </div>
            </div>
        </div>

        <!-- Clients with Complete Profile Card -->
        <div class="overflow-hidden rounded-lg bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-amber-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-600" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="font-semibold text-gray-500">Complete Profiles</h2>
                    <p class="text-2xl font-bold text-gray-800">@(Model.Clients?.Count(c => !string.IsNullOrEmpty(c.PhoneNumber) && !string.IsNullOrEmpty(c.Gender) && !string.IsNullOrEmpty(c.DateOfBirth)) ?? 0)</p>
                </div>
            </div>
        </div>

        <!-- Current Page Card -->
        <div class="overflow-hidden rounded-lg bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="font-semibold text-gray-500">Page</h2>
                    <p class="text-2xl font-bold text-gray-800">@(Model.Pagination?.CurrentPage ?? 1) of @(Model.Pagination?.TotalPages ?? 1)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Dropdown Menus (Positioned outside table to avoid clipping) -->
    @if (Model.Clients != null)
    {
        @for (int j = 0; j < Model.Clients.Count; j++)
        {
            var client = Model.Clients[j];
            <div class="fixed z-50 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden"
                 role="menu"
                 aria-orientation="vertical"
                 aria-labelledby="<EMAIL>"
                 id="<EMAIL>"
                 style="z-index: 9999;">
                <div class="py-1" role="none">
                    <a href="#" onclick="viewClientProfile('@client.UserId')" class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-900" role="menuitem">
                        <svg class="mr-3 h-5 w-5 text-indigo-600 group-hover:text-indigo-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        View Profile
                    </a>
                    <a href="#" onclick="editClient('@client.UserId')" class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-900" role="menuitem">
                        <svg class="mr-3 h-5 w-5 text-indigo-600 group-hover:text-indigo-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                        Edit Client
                    </a>
                    <a href="#" onclick="viewBookings('@client.UserId')" class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-900" role="menuitem">
                        <svg class="mr-3 h-5 w-5 text-indigo-600 group-hover:text-indigo-900" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        View Bookings
                    </a>
                </div>
            </div>
        }
    }

    <!-- Clients Table -->
    <div class="overflow-hidden rounded-lg bg-white shadow-sm">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            <a href="@Url.Action("Clients", new { page = 1, searchTerm = Model.SearchString, sortBy = "name", sortDescending = Model.SortBy == "name" ? !Model.SortDescending : true })"
                               class="group inline-flex items-center">
                                Client
                                @if (Model.SortBy == "name")
                                {
                                    @if (Model.SortDescending)
                                    {
                                        <svg class="ml-1 h-4 w-4 text-gray-400 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    }
                                    else
                                    {
                                        <svg class="ml-1 h-4 w-4 text-gray-400 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
                                        </svg>
                                    }
                                }
                                else
                                {
                                    <svg class="ml-1 h-4 w-4 text-gray-200 group-hover:text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                }
                            </a>
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            <a href="@Url.Action("Clients", new { page = 1, searchTerm = Model.SearchString, sortBy = "email", sortDescending = Model.SortBy == "email" ? !Model.SortDescending : true })"
                               class="group inline-flex items-center">
                                Contact Information
                                @if (Model.SortBy == "email")
                                {
                                    @if (Model.SortDescending)
                                    {
                                        <svg class="ml-1 h-4 w-4 text-gray-400 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                        </svg>
                                    }
                                    else
                                    {
                                        <svg class="ml-1 h-4 w-4 text-gray-400 group-hover:text-gray-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z" clip-rule="evenodd" />
                                        </svg>
                                    }
                                }
                                else
                                {
                                    <svg class="ml-1 h-4 w-4 text-gray-200 group-hover:text-gray-300" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                    </svg>
                                }
                            </a>
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500">
                            Personal Details
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                    @if (Model.Clients == null || Model.Clients.Count == 0)
                    {
                        <tr>
                            <td colspan="4" class="px-6 py-4 text-center text-sm text-gray-500">
                                No clients found.
                            </td>
                        </tr>
                    }
                    else
                    {
                        @for (int i = 0; i < Model.Clients.Count; i++)
                        {
                            var client = Model.Clients[i];
                            <tr class="hover:bg-gray-50">
                                <!-- Client Profile Column -->
                                <td class="whitespace-nowrap px-6 py-4">
                                    <div class="flex items-center">
                                        @if (!string.IsNullOrEmpty(client.ProfilePictureUrl))
                                        {
                                            <img class="h-12 w-12 rounded-full object-cover ring-2 ring-gray-200"
                                                 src="@client.ProfilePictureUrl"
                                                 alt="@client.Name avatar"
                                                 onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">
                                            <div class="hidden h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-indigo-100 text-indigo-700 ring-2 ring-gray-200">
                                                @{
                                                    var initials = string.IsNullOrEmpty(client.Name) ? "CL" :
                                                        string.Join("", client.Name.Split(' ').Select(n => n.Length > 0 ? n[0].ToString() : "").Take(2));
                                                }
                                                <span class="text-sm font-medium">@initials</span>
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-indigo-100 text-indigo-700 ring-2 ring-gray-200">
                                                @{
                                                    var initials = string.IsNullOrEmpty(client.Name) ? "CL" :
                                                        string.Join("", client.Name.Split(' ').Select(n => n.Length > 0 ? n[0].ToString() : "").Take(2));
                                                }
                                                <span class="text-sm font-medium">@initials</span>
                                            </div>
                                        }
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">@(string.IsNullOrEmpty(client.Name) ? "No Name" : client.Name)</div>
                                            <div class="text-sm text-gray-500">ID: @client.UserId.Substring(0, 8)...</div>
                                        </div>
                                    </div>
                                </td>

                                <!-- Contact Information Column -->
                                <td class="whitespace-nowrap px-6 py-4">
                                    <div class="text-sm text-gray-900">@(string.IsNullOrEmpty(client.Email) ? "Not provided" : client.Email)</div>
                                    <div class="text-sm text-gray-500">@(string.IsNullOrEmpty(client.PhoneNumber) ? "No phone" : client.PhoneNumber)</div>
                                </td>

                                <!-- Personal Details Column -->
                                <td class="whitespace-nowrap px-6 py-4">
                                    <div class="text-sm text-gray-900">
                                        @if (!string.IsNullOrEmpty(client.Gender))
                                        {
                                            <span>@client.Gender</span>
                                        }
                                        else
                                        {
                                            <span class="text-gray-500">Gender not specified</span>
                                        }
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        @if (!string.IsNullOrEmpty(client.DateOfBirth))
                                        {
                                            @if (DateTime.TryParse(client.DateOfBirth, out var birthDate))
                                            {
                                                var age = DateTime.Now.Year - birthDate.Year;
                                                if (DateTime.Now.DayOfYear < birthDate.DayOfYear) age--;
                                                <span>Age: @age years</span>
                                            }
                                            else
                                            {
                                                <span>DOB: @client.DateOfBirth</span>
                                            }
                                        }
                                        else
                                        {
                                            <span>Age not provided</span>
                                        }
                                    </div>
                                </td>

                                <!-- Actions Column -->
                                <td class="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                                    <div class="relative inline-block text-left">
                                        <button type="button"
                                                class="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                                                id="<EMAIL>"
                                                aria-expanded="false"
                                                aria-haspopup="true"
                                                onclick="toggleDropdown('@client.UserId')">
                                            Actions
                                            <svg class="-mr-1 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modern Pagination -->
    @if (Model.Pagination != null)
    {
        <div class="mt-6 flex flex-col sm:flex-row items-center justify-between">
            <!-- Pagination Info -->
            <div class="text-sm text-gray-700 mb-4 sm:mb-0 text-center sm:text-left">
                Showing <span class="font-medium">@(((Model.Pagination.CurrentPage - 1) * Model.Pagination.PageSize) + 1)</span>
                to <span class="font-medium">@Math.Min(Model.Pagination.CurrentPage * Model.Pagination.PageSize, Model.Pagination.TotalCount)</span>
                of <span class="font-medium">@Model.Pagination.TotalCount</span> clients
            </div>

            <!-- Pagination Controls -->
            <nav class="flex justify-center sm:justify-end" aria-label="Pagination">
                <ul class="inline-flex items-center -space-x-px">
                    <!-- First Page -->
                    <li>
                        <a href="@Url.Action("Clients", new { page = 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })"
                           class="@(Model.Pagination.CurrentPage == 1 ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "bg-white text-gray-500 hover:bg-gray-50") inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-300 rounded-l-md"
                           aria-label="First page">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M15.79 14.77a.75.75 0 01-1.06.02L10 10.25l-4.73 4.52a.75.75 0 01-1.04-1.08l5.25-5a.75.75 0 011.04 0l5.25 5a.75.75 0 01.02 1.06z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>

                    <!-- Previous Page -->
                    <li>
                        <a href="@(Model.Pagination.HasPreviousPage ? Url.Action("Clients", new { page = Model.Pagination.CurrentPage - 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending }) : "#")"
                           class="@(Model.Pagination.HasPreviousPage ? "bg-white text-gray-500 hover:bg-gray-50" : "bg-gray-100 text-gray-400 cursor-not-allowed") inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-300"
                           aria-label="Previous page">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>

                    <!-- Page Numbers -->
                    @{
                        int startPage = Math.Max(1, Model.Pagination.CurrentPage - 2);
                        int endPage = Math.Min(Model.Pagination.TotalPages, startPage + 4);
                        if (endPage - startPage < 4)
                        {
                            startPage = Math.Max(1, endPage - 4);
                        }
                    }
                    @for (int i = startPage; i <= endPage; i++)
                    {
                        <li>
                            <a href="@Url.Action("Clients", new { page = i, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })"
                               class="@(Model.Pagination.CurrentPage == i ? "bg-indigo-600 text-white border-indigo-600" : "bg-white text-gray-500 hover:bg-gray-50 border-gray-300") inline-flex items-center px-4 py-2 text-sm font-medium border"
                               aria-label="Page @i">
                                @i
                            </a>
                        </li>
                    }

                    <!-- Next Page -->
                    <li>
                        <a href="@(Model.Pagination.HasNextPage ? Url.Action("Clients", new { page = Model.Pagination.CurrentPage + 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending }) : "#")"
                           class="@(Model.Pagination.HasNextPage ? "bg-white text-gray-500 hover:bg-gray-50" : "bg-gray-100 text-gray-400 cursor-not-allowed") inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-300"
                           aria-label="Next page">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>

                    <!-- Last Page -->
                    <li>
                        <a href="@Url.Action("Clients", new { page = Model.Pagination.TotalPages, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })"
                           class="@(Model.Pagination.CurrentPage == Model.Pagination.TotalPages ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "bg-white text-gray-500 hover:bg-gray-50") inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-300 rounded-r-md"
                           aria-label="Last page">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4.21 5.23a.75.75 0 011.06-.02L10 9.75l4.73-4.52a.75.75 0 011.04 1.08l-5.25 5a.75.75 0 01-1.04 0l-5.25-5a.75.75 0 01-.02-1.06z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    }
</div>

<!-- Client Profile Modal -->
<div id="clientProfileModal" class="fixed inset-0 z-[9999] hidden overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeClientProfileModal()"></div>

    <div class="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div class="bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="w-full">
                        <!-- Modal Header -->
                        <div class="flex justify-between items-center mb-4">
                            <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                                Client Profile
                            </h3>
                            <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeClientProfileModal()">
                                <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>

                        <!-- Loading State -->
                        <div id="profileLoading" class="text-center py-8">
                            <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                            <p class="mt-2 text-sm text-gray-500">Loading profile...</p>
                        </div>

                        <!-- Error State -->
                        <div id="profileError" class="hidden text-center py-8">
                            <div class="text-red-500 mb-2">
                                <svg class="mx-auto h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                                </svg>
                            </div>
                            <p id="profileErrorMessage" class="text-sm text-gray-500"></p>
                        </div>

                        <!-- Profile Content -->
                        <div id="profileContent" class="hidden">
                            <!-- Profile Header -->
                            <div class="flex items-center mb-6">
                                <div id="profilePictureContainer" class="flex-shrink-0 mr-4">
                                    <!-- Profile picture will be inserted here -->
                                </div>
                                <div>
                                    <h4 id="profileName" class="text-xl font-semibold text-gray-900"></h4>
                                    <p id="profileEmail" class="text-sm text-gray-500"></p>
                                </div>
                            </div>

                            <!-- Profile Details Grid -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <!-- Personal Information -->
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900 mb-3">Personal Information</h5>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-xs font-medium text-gray-500">Phone Number</dt>
                                            <dd id="profilePhone" class="text-sm text-gray-900"></dd>
                                        </div>
                                        <div>
                                            <dt class="text-xs font-medium text-gray-500">Gender</dt>
                                            <dd id="profileGender" class="text-sm text-gray-900"></dd>
                                        </div>
                                        <div>
                                            <dt class="text-xs font-medium text-gray-500">Date of Birth</dt>
                                            <dd id="profileDOB" class="text-sm text-gray-900"></dd>
                                        </div>
                                        <div>
                                            <dt class="text-xs font-medium text-gray-500">Age</dt>
                                            <dd id="profileAge" class="text-sm text-gray-900"></dd>
                                        </div>
                                    </dl>
                                </div>

                                <!-- Address Information -->
                                <div>
                                    <h5 class="text-sm font-medium text-gray-900 mb-3">Address Information</h5>
                                    <dl class="space-y-2">
                                        <div>
                                            <dt class="text-xs font-medium text-gray-500">Street Address</dt>
                                            <dd id="profileStreetAddress" class="text-sm text-gray-900"></dd>
                                        </div>
                                        <div>
                                            <dt class="text-xs font-medium text-gray-500">City</dt>
                                            <dd id="profileCity" class="text-sm text-gray-900"></dd>
                                        </div>
                                        <div>
                                            <dt class="text-xs font-medium text-gray-500">State</dt>
                                            <dd id="profileState" class="text-sm text-gray-900"></dd>
                                        </div>
                                        <div>
                                            <dt class="text-xs font-medium text-gray-500">Postal Code</dt>
                                            <dd id="profilePostalCode" class="text-sm text-gray-900"></dd>
                                        </div>
                                    </dl>
                                </div>
                            </div>

                            <!-- Documents Section -->
                            <div class="mt-6">
                                <h5 class="text-sm font-medium text-gray-900 mb-3">Documents</h5>
                                <div id="profileDocuments" class="space-y-2">
                                    <!-- Documents will be inserted here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-indigo-600 text-base font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 sm:ml-3 sm:w-auto sm:text-sm" onclick="closeClientProfileModal()">
                    Close
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Clients page functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Clients page loaded successfully');

            // Close dropdowns when clicking outside
            document.addEventListener('click', function(event) {
                const dropdowns = document.querySelectorAll('[id^="dropdown-"]');
                dropdowns.forEach(dropdown => {
                    const button = document.querySelector(`[id="menu-button-${dropdown.id.replace('dropdown-', '')}"]`);
                    if (button && !button.contains(event.target) && !dropdown.contains(event.target)) {
                        dropdown.classList.add('hidden');
                        button.setAttribute('aria-expanded', 'false');
                    }
                });
            });
        });

        // REVISED Toggle dropdown function with dynamic positioning
        function toggleDropdown(clientId) {
            const dropdown = document.getElementById(`dropdown-${clientId}`);
            const button = document.getElementById(`menu-button-${clientId}`);

            if (!dropdown || !button) {
                console.error('Dropdown or button not found for client ID:', clientId);
                return;
            }

            // Close all other dropdowns first
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => {
                if (dd.id !== `dropdown-${clientId}`) {
                    dd.classList.add('hidden');
                    const otherButton = document.getElementById(`menu-button-${dd.id.replace('dropdown-', '')}`);
                    if (otherButton) {
                        otherButton.setAttribute('aria-expanded', 'false');
                    }
                }
            });

            if (dropdown.classList.contains('hidden')) {
                // Temporarily make it visible (but not to user) to measure its dimensions
                dropdown.style.visibility = 'hidden';
                dropdown.style.display = 'block'; // Or 'flex', 'grid' etc. depending on its layout, 'block' is usually fine

                const dropdownWidth = dropdown.offsetWidth;
                const dropdownHeight = dropdown.offsetHeight;

                // Restore its state before actual positioning and display
                dropdown.style.visibility = '';
                dropdown.style.display = ''; // The 'hidden' class will take over again if still present

                const buttonRect = button.getBoundingClientRect();
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                const gap = 5; // Small gap from button and viewport edges

                let finalTop, finalLeft;

                // --- Vertical Positioning ---
                // Prefer below button
                if (buttonRect.bottom + dropdownHeight + gap <= viewportHeight) {
                    finalTop = buttonRect.bottom + gap;
                }
                // Else, try above button
                else if (buttonRect.top - dropdownHeight - gap >= 0) {
                    finalTop = buttonRect.top - dropdownHeight - gap;
                }
                // Else, it doesn't fit nicely above or below.
                // Fallback: align top with button top, then ensure it's within viewport bounds.
                else {
                    finalTop = buttonRect.top;
                }
                // Ensure finalTop is within viewport bounds as much as possible
                finalTop = Math.max(gap, Math.min(finalTop, viewportHeight - dropdownHeight - gap));


                // --- Horizontal Positioning (Attempt to align right edge of dropdown with right edge of button by default) ---
                finalLeft = buttonRect.right - dropdownWidth;

                // If aligning right-to-right makes it overflow to the left of viewport,
                // try aligning dropdown's left with button's left.
                if (finalLeft < gap) {
                    finalLeft = buttonRect.left;
                }

                // After choosing an initial alignment, ensure it doesn't overflow viewport edges.
                // Check right overflow
                if (finalLeft + dropdownWidth + gap > viewportWidth) {
                    finalLeft = viewportWidth - dropdownWidth - gap;
                }
                // Check left overflow (again, in case viewport is narrower than dropdown or initial position was bad)
                if (finalLeft < gap) {
                    finalLeft = gap;
                }
                
                // Apply the calculated position
                // `position: fixed` and `z-index` are handled by existing classes/styles on the dropdown div.
                dropdown.style.top = `${finalTop}px`;
                dropdown.style.left = `${finalLeft}px`;

                dropdown.classList.remove('hidden'); // Show the dropdown
                button.setAttribute('aria-expanded', 'true');
            } else {
                dropdown.classList.add('hidden');
                button.setAttribute('aria-expanded', 'false');
            }
        }

        // View client profile function
        async function viewClientProfile(clientId) {
            console.log('Viewing profile for client:', clientId);

            // Close any open dropdowns
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => {
                dd.classList.add('hidden');
                const button = document.getElementById(`menu-button-${dd.id.replace('dropdown-', '')}`);
                if (button) button.setAttribute('aria-expanded', 'false');
            });

            // Show modal
            document.getElementById('clientProfileModal').classList.remove('hidden');
            document.body.style.overflow = 'hidden'; // Prevent background scrolling

            // Reset modal state
            document.getElementById('profileLoading').classList.remove('hidden');
            document.getElementById('profileError').classList.add('hidden');
            document.getElementById('profileContent').classList.add('hidden');

            try {
                const response = await fetch(`/Dashboard/GetClientProfile?clientId=${encodeURIComponent(clientId)}`);

                if (!response.ok) {
                     const errorData = await response.json().catch(() => ({ message: `HTTP error! status: ${response.status}` }));
                    throw new Error(errorData.message || `HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                console.log('Profile data received:', data);

                if (data.success && data.payload) {
                    populateProfileModal(data.payload);
                } else {
                    showProfileError(data.message || 'Failed to load profile data.');
                }
            } catch (error) {
                console.error('Error fetching profile:', error);
                showProfileError(error.message || 'Failed to load profile. Please try again.');
            }
        }

        // Edit client function
        function editClient(clientId) {
            console.log('Editing client:', clientId);
            // Close any open dropdowns
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => {
                dd.classList.add('hidden');
                const button = document.getElementById(`menu-button-${dd.id.replace('dropdown-', '')}`);
                if (button) button.setAttribute('aria-expanded', 'false');
            });

            // Add your edit logic here
            // Consider redirecting to an edit page:
            // window.location.href = `/Dashboard/EditClient/${clientId}`;
            alert('Edit client functionality to be implemented. Redirect to edit page for client ' + clientId);
        }

        // View bookings function
        function viewBookings(clientId) {
            console.log('Viewing bookings for client:', clientId);
            // Close any open dropdowns
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => {
                dd.classList.add('hidden');
                const button = document.getElementById(`menu-button-${dd.id.replace('dropdown-', '')}`);
                if (button) button.setAttribute('aria-expanded', 'false');
            });

            // Add your view bookings logic here
            // Example: Redirect to a bookings page filtered by client
            // window.location.href = `/Dashboard/ClientBookings?clientId=${clientId}`;
            alert('View bookings functionality to be implemented for client ' + clientId);
        }

        // Populate profile modal with data
        function populateProfileModal(profile) {
            // Hide loading, show content
            document.getElementById('profileLoading').classList.add('hidden');
            document.getElementById('profileError').classList.add('hidden');
            document.getElementById('profileContent').classList.remove('hidden');

            // Profile header
            document.getElementById('profileName').textContent = profile.name || 'N/A';
            document.getElementById('profileEmail').textContent = profile.email || 'N/A';

            // Profile picture
            const pictureContainer = document.getElementById('profilePictureContainer');
            pictureContainer.innerHTML = ''; // Clear previous
            if (profile.profilePictureUrl) {
                const img = document.createElement('img');
                img.className = "h-16 w-16 rounded-full object-cover ring-2 ring-gray-200";
                img.src = profile.profilePictureUrl;
                img.alt = (profile.name || 'Client') + " avatar";
                img.onerror = function() { // Fallback if image fails to load
                    this.remove();
                    const initials = (profile.name ? profile.name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase() : 'CL');
                    pictureContainer.innerHTML = `<div class="flex h-16 w-16 items-center justify-center rounded-full bg-indigo-100 text-indigo-700 ring-2 ring-gray-200"><span class="text-lg font-medium">${initials}</span></div>`;
                };
                pictureContainer.appendChild(img);
            } else {
                const initials = (profile.name ? profile.name.split(' ').map(n => n[0]).join('').substring(0, 2).toUpperCase() : 'CL');
                pictureContainer.innerHTML = `<div class="flex h-16 w-16 items-center justify-center rounded-full bg-indigo-100 text-indigo-700 ring-2 ring-gray-200"><span class="text-lg font-medium">${initials}</span></div>`;
            }

            // Personal information
            document.getElementById('profilePhone').textContent = profile.phoneNumber || 'N/A';
            document.getElementById('profileGender').textContent = profile.gender || 'N/A';

            // Date of birth and age
            if (profile.dateOfBirth) {
                try {
                    const dob = new Date(profile.dateOfBirth);
                    if (isNaN(dob.getTime())) throw new Error("Invalid date"); // Check if date is valid
                    document.getElementById('profileDOB').textContent = dob.toLocaleDateString(undefined, { year: 'numeric', month: 'long', day: 'numeric' });

                    const today = new Date();
                    let age = today.getFullYear() - dob.getFullYear();
                    const monthDiff = today.getMonth() - dob.getMonth();
                    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < dob.getDate())) {
                        age--;
                    }
                    document.getElementById('profileAge').textContent = age >= 0 ? `${age} years` : 'N/A';
                } catch (e) {
                    document.getElementById('profileDOB').textContent = 'Invalid Date';
                    document.getElementById('profileAge').textContent = 'N/A';
                }
            } else {
                document.getElementById('profileDOB').textContent = 'N/A';
                document.getElementById('profileAge').textContent = 'N/A';
            }

            // Address information
            const address = profile.primaryAddress || {};
            document.getElementById('profileStreetAddress').textContent = address.streetAddress || 'N/A';
            document.getElementById('profileCity').textContent = address.city || 'N/A';
            document.getElementById('profileState').textContent = address.state || 'N/A';
            document.getElementById('profilePostalCode').textContent = address.postalCode || 'N/A';
            

            // Documents
            const documentsContainer = document.getElementById('profileDocuments');
            documentsContainer.innerHTML = ''; // Clear previous
            if (profile.documents && profile.documents.length > 0) {
                profile.documents.forEach(doc => {
                    const docElement = document.createElement('div');
                    docElement.className = "flex items-center justify-between p-2 bg-gray-50 rounded hover:bg-gray-100";
                    // Basic display, if URL is available, make it a link
                    let docDisplay = `<span class="text-sm text-gray-900 truncate" title="${doc.name || 'Document'}">${doc.name || 'Document'}</span>
                                      <span class="text-xs text-gray-500 ml-2 flex-shrink-0">${doc.type || 'File'}</span>`;
                    if (doc.url) { // Assuming doc.url holds the link to the document
                        docElement.innerHTML = `<a href="${doc.url}" target="_blank" rel="noopener noreferrer" class="flex items-center justify-between w-full group">${docDisplay}
                                                <svg class="ml-2 h-4 w-4 text-indigo-500 group-hover:text-indigo-700 opacity-0 group-hover:opacity-100 transition-opacity" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path></svg>
                                                </a>`;
                    } else {
                         docElement.innerHTML = docDisplay;
                    }
                    documentsContainer.appendChild(docElement);
                });
            } else {
                documentsContainer.innerHTML = '<p class="text-sm text-gray-500">No documents uploaded.</p>';
            }
        }

        // Show profile error
        function showProfileError(message) {
            document.getElementById('profileLoading').classList.add('hidden');
            document.getElementById('profileContent').classList.add('hidden');
            document.getElementById('profileError').classList.remove('hidden');
            document.getElementById('profileErrorMessage').textContent = message;
        }

        // Close profile modal
        function closeClientProfileModal() {
            document.getElementById('clientProfileModal').classList.add('hidden');
            document.body.style.overflow = ''; // Restore background scrolling
        }
    </script>
}

