﻿using Microsoft.AspNetCore.Mvc;
using Northwind.Services.Auth;
using Northwind.ViewModels;

namespace Northwind.Controllers
{
    public class AuthController : Controller
    {
        private readonly IJwtAuthService _authService;

        public AuthController(IJwtAuthService authService)
        {
            _authService = authService;
        }

        [HttpPost]
        public async Task<IActionResult> Logout()
        {
            await _authService.LogoutAsync();
            return RedirectToAction("Login");
        }

        [HttpGet]
        public IActionResult Login()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> Login(LoginVm model)
        {
            if (ModelState.IsValid)
            {
                var result = await _authService.LoginAsync(model.Username, model.Password);

                if (result.Success)
                {
                    return RedirectToAction("Index", "Dashboard");
                }

                TempData["ErrorMessage"] = result.Message;
            }

            return View(model);
        }

        [HttpGet]
        public IActionResult Register()
        {
            return View();
        }

        [HttpPost]
        public IActionResult Register(RegisterVm registerVm)
        {
            if (ModelState.IsValid)
            {
                // In a real implementation, you would call the API to register the user
                // For now, we'll just redirect to the login page with a success message
                TempData["SuccessMessage"] =
                    "Registration successful! Please login with your credentials.";
                return RedirectToAction("Login");
            }

            return View(registerVm);
        }

        [HttpGet]
        public IActionResult ForgotPassword()
        {
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> ForgotPassword(ForgotPasswordVm model)
        {
            if (ModelState.IsValid)
            {
                var otpModel = new OTPVerificationVm { Email = model.Email };
                await Task.Yield();
                // Simulate sending an OTP code
                TempData["SuccessMessage"] =
                    "Verification code sent to your email. Please check your inbox.";

                return View("OTPVerification", otpModel);
            }

            return View(model);
        }

        [HttpGet]
        public IActionResult OtpVerfication(string email)
        {
            var model = new OTPVerificationVm { Email = email };
            return View(model);
        }

        [HttpPost]
        public IActionResult OtpVerfication(OTPVerificationVm model)
        {
            if (ModelState.IsValid)
            {
                // In a real application, you would:
                // 1. Verify the OTP code against what's stored in your database
                // 2. Check if the OTP code is still valid (not expired)
                // 3. If valid, allow the user to reset their password

                // For demo purposes, we'll just accept any 6-digit code
                // and redirect to the reset password page
                if (model.OTPCode.Length == 6 && model.OTPCode.All(char.IsDigit))
                {
                    var resetModel = new ResetPasswordVm
                    {
                        Email = model.Email,
                        ResetToken = Guid.NewGuid().ToString(), // In a real app, this would be a secure token
                    };

                    TempData["SuccessMessage"] =
                        "OTP verified successfully. Please reset your password.";

                    return View("ResetPassword", resetModel);
                }

                TempData["ErrorMessage"] = "Invalid verification code. Please try again.";
            }

            return View(model);
        }

        [HttpGet]
        public IActionResult ResetPassword(string email, string token)
        {
            var model = new ResetPasswordVm { Email = email, ResetToken = token };
            return View(model);
        }

        [HttpPost]
        public async Task<IActionResult> ResetPassword(ResetPasswordVm model)
        {
            if (ModelState.IsValid)
            {
                // In a real application, you would:
                // 1. Verify the reset token is valid and not expired
                // 2. Find the user by email
                // 3. Reset the user's password

                // For demo purposes, we'll just show a success message and redirect to login
                await Task.Yield();
                TempData["SuccessMessage"] =
                    "Your password has been reset successfully. Please login with your new password.";
                return RedirectToAction("Login");
            }

            return View(model);
        }
    }
}
