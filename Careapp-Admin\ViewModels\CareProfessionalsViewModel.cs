using Northwind.Models.Admin;
using Northwind.Models.Professionals;

namespace Northwind.ViewModels
{
    public class CareProfessionalsViewModel
    {
        public List<CareProvider> CareProviders { get; set; }
        public PaginationMeta Pagination { get; set; }
        public string ErrorMessage { get; set; }
        public string SearchString { get; set; }
        public string SortBy { get; set; } = "createdAt";
        public bool SortDescending { get; set; } = true;

        public CareProfessionalsViewModel()
        {
            CareProviders = new List<CareProvider>();
        }

        // Helper method to get verification status display class
        public string GetVerificationStatusClass(string status)
        {
            return status?.ToLower() switch
            {
                "verified" => "bg-green-100 text-green-800",
                "pending" => "bg-yellow-100 text-yellow-800",
                "rejected" => "bg-red-100 text-red-800",
                _ => "bg-gray-100 text-gray-800",
            };
        }
    }
}
