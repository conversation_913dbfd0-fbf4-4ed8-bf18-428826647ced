using System.Text.Json.Serialization;

namespace Northwind.Models.Auth
{
    public class AuthResponse
    {
        [JsonPropertyName("apiResponseId")]
        public string ApiResponseId { get; set; }

        [JsonPropertyName("success")]
        public bool Success { get; set; }

        [Json<PERSON>ropertyName("statusCode")]
        public int StatusCode { get; set; }

        [JsonPropertyName("message")]
        public string Message { get; set; }

        [JsonPropertyName("payload")]
        public AuthPayload Payload { get; set; }

        [JsonPropertyName("timestamp")]
        public DateTime Timestamp { get; set; }
    }

    public class AuthPayload
    {
        [JsonPropertyName("accessToken")]
        public string AccessToken { get; set; }

        [Json<PERSON>ropertyName("expiresIn")]
        public DateTime ExpiresIn { get; set; }

        [Json<PERSON>ropertyName("refreshToken")]
        public string RefreshToken { get; set; }

        [Json<PERSON>ropertyName("isVerifiedByAdmin")]
        public bool? IsVerifiedByAdmin { get; set; }
    }
}
