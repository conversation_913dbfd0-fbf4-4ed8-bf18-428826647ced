namespace Northwind.Configuration
{
    public class ApiSettings
    {
        public string BaseUrl { get; set; } = string.Empty;
        public string AuthEndpoint { get; set; } = string.Empty;
        public string RefreshTokenEndpoint { get; set; } = string.Empty;
        public string LogoutEndpoint { get; set; } = string.Empty;

        // Token will be refreshed if it expires within this many minutes
        public int RefreshTokenThresholdMinutes { get; set; } = 30;
    }
}
