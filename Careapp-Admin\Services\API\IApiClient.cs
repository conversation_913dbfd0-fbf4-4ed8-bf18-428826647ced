namespace Northwind.Services.API;

/// <summary>
/// Interface for the base API client
/// </summary>
public interface IApiClient
{
    /// <summary>
    /// Sends a GET request to the specified endpoint
    /// </summary>
    /// <typeparam name="T">The type to deserialize the response to</typeparam>
    /// <param name="endpoint">The API endpoint</param>
    /// <param name="parameters">Optional query parameters</param>
    /// <returns>The deserialized response</returns>
    Task<T> GetAsync<T>(string endpoint, Dictionary<string, string>? parameters = null);

    /// <summary>
    /// Sends a POST request to the specified endpoint
    /// </summary>
    /// <typeparam name="T">The type to deserialize the response to</typeparam>
    /// <typeparam name="TRequest">The type of the request body</typeparam>
    /// <param name="endpoint">The API endpoint</param>
    /// <param name="data">The request body</param>
    /// <returns>The deserialized response</returns>
    Task<T> PostAsync<T, TRequest>(string endpoint, TRequest data);

    /// <summary>
    /// Sends a PUT request to the specified endpoint
    /// </summary>
    /// <typeparam name="T">The type to deserialize the response to</typeparam>
    /// <typeparam name="TRequest">The type of the request body</typeparam>
    /// <param name="endpoint">The API endpoint</param>
    /// <param name="data">The request body</param>
    /// <returns>The deserialized response</returns>
    Task<T> PutAsync<T, TRequest>(string endpoint, TRequest data);

    /// <summary>
    /// Sends a DELETE request to the specified endpoint
    /// </summary>
    /// <typeparam name="T">The type to deserialize the response to</typeparam>
    /// <param name="endpoint">The API endpoint</param>
    /// <returns>The deserialized response</returns>
    Task<T> DeleteAsync<T>(string endpoint);
}
