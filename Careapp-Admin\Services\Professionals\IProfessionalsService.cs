using System.Threading.Tasks;
using Northwind.Models.Admin;
using Northwind.Models.Professionals;

namespace Northwind.Services.Professionals
{
    public interface IProfessionalsService
    {
        Task<CareProviderResponse> GetCareProvidersAsync(
            int page = 1,
            int pageSize = 10,
            string searchTerm = "",
            string sortBy = "createdAt",
            bool sortDescending = true
        );

        /// <summary>
        /// Gets pending approval requests for a specific care provider
        /// </summary>
        /// <param name="userId">The user ID of the care provider</param>
        /// <returns>Response containing approval requests</returns>
        Task<ApprovalRequestResponse> GetProviderApprovalRequestsAsync(string userId);

        /// <summary>
        /// Approves a care provider verification request
        /// </summary>
        /// <param name="requestId">The approval request ID</param>
        /// <param name="notes">Optional administrative notes</param>
        /// <returns>Response indicating success or failure</returns>
        Task<ApprovalActionResponse> ApproveProviderAsync(string requestId, string notes = null);

        /// <summary>
        /// Rejects a care provider verification request
        /// </summary>
        /// <param name="requestId">The approval request ID</param>
        /// <param name="rejectionReason">Required reason for rejection</param>
        /// <param name="notes">Optional additional notes</param>
        /// <returns>Response indicating success or failure</returns>
        Task<ApprovalActionResponse> RejectProviderAsync(
            string requestId,
            string rejectionReason,
            string notes = null
        );
    }
}
