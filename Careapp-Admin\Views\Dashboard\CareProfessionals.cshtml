@model Northwind.ViewModels.CareProfessionalsViewModel
@{
    ViewData["Title"] = "Care Professionals";
    Layout = "Layouts/_LayoutsDash";
}

<partial name="_SuccessNotification" />
<partial name="_ErrorNotification" />
<partial name="_InfoNotification" />

<div class="p-6">
    <!-- Enhanced Responsive Header Section -->
    <div class="mb-6 flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <h1 class="text-2xl font-bold text-gray-800">Care Professionals Management</h1>
        <div class="mt-4 w-full sm:w-auto sm:mt-0">
            <form asp-controller="Dashboard" asp-action="CareProfessionals" method="get" class="flex flex-col sm:flex-row items-center gap-2">
                <div class="relative w-full sm:w-auto">
                    <input type="text" name="searchTerm" value="@Model.SearchString"
                        class="w-full sm:w-64 rounded-lg border border-gray-300 py-2 pl-10 pr-4 focus:border-indigo-500 focus:outline-none focus:ring-1 focus:ring-indigo-500"
                        placeholder="Search care professionals..."
                        aria-label="Search care professionals">
                    <div class="absolute inset-y-0 left-0 flex items-center pl-3">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20"
                            fill="currentColor" aria-hidden="true">
                            <path fill-rule="evenodd"
                                d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z"
                                clip-rule="evenodd" />
                        </svg>
                    </div>
                </div>
                <!-- Hidden fields to preserve other query parameters -->
                <input type="hidden" name="sortBy" value="@Model.SortBy" />
                <input type="hidden" name="sortDescending" value="@Model.SortDescending.ToString().ToLower()" />
                <button type="submit"
                    class="w-full sm:w-auto rounded-lg bg-indigo-600 px-4 py-2 text-sm font-medium text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                    aria-label="Submit search">
                    Search
                </button>
            </form>
        </div>
    </div>

    <!-- Dropdown Menus (positioned outside table to avoid clipping) -->
    @for (int i = 0; i < Model.CareProviders.Count; i++)
    {
        var provider = Model.CareProviders[i];
        <div class="fixed z-50 mt-2 w-56 origin-top-right divide-y divide-gray-100 rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none hidden"
            role="menu" aria-orientation="vertical" aria-labelledby="menu-button-@(i)" id="dropdown-@(i)"
            style="z-index: 9999;">
            <div class="py-1" role="none">
                <a href="#" onclick="viewDetails('@(i)')"
                    class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-900"
                    role="menuitem">
                    <svg class="mr-3 h-5 w-5 text-indigo-600 group-hover:text-indigo-900" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                    View Details
                </a>
                <a href="#" onclick="editProvider('@(i)')"
                    class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-900"
                    role="menuitem">
                    <svg class="mr-3 h-5 w-5 text-indigo-600 group-hover:text-indigo-900" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    Edit
                </a>
            </div>
            <div class="py-1" role="none">
                @{
                    // Normalize the verification status for comparison
                    var status = provider.VerificationStatus?.ToLower();
                }
                @if (status != "verified") // Hide "Approve" if status is "Verified"
                {
                    <a href="#" onclick="openApproveModal('@(i)', '@(provider.ProviderId ?? provider.UserId)')"
                        class="group flex items-center px-4 py-2 text-sm text-green-700 hover:bg-green-50 hover:text-green-900"
                        role="menuitem">
                        <svg class="mr-3 h-5 w-5 text-green-600 group-hover:text-green-900" xmlns="http://www.w3.org/2000/svg"
                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M5 13l4 4L19 7" />
                        </svg>
                        Approve Provider
                    </a>
                }
                @if (status != "rejected") // Hide "Reject" if status is "Rejected"
                {
                    <a href="#" onclick="openRejectModal('@(i)', '@(provider.ProviderId ?? provider.UserId)')"
                        class="group flex items-center px-4 py-2 text-sm text-orange-700 hover:bg-orange-50 hover:text-orange-900"
                        role="menuitem">
                        <svg class="mr-3 h-5 w-5 text-orange-600 group-hover:text-orange-900" xmlns="http://www.w3.org/2000/svg"
                            fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M6 18L18 6M6 6l12 12" />
                        </svg>
                        Reject Provider
                    </a>
                }
                <a href="#"
                    class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-900"
                    role="menuitem">
                    <svg class="mr-3 h-5 w-5 text-indigo-600 group-hover:text-indigo-900" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
                    </svg>
                    Activate/Suspend Account
                </a>
                <a href="#"
                    class="group flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-indigo-50 hover:text-indigo-900"
                    role="menuitem">
                    <svg class="mr-3 h-5 w-5 text-indigo-600 group-hover:text-indigo-900" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    View Documents
                </a>
            </div>
            <div class="py-1" role="none">
                <a href="#" onclick="deleteProvider('@(i)')"
                    class="group flex items-center px-4 py-2 text-sm text-red-700 hover:bg-red-50 hover:text-red-900"
                    role="menuitem">
                    <svg class="mr-3 h-5 w-5 text-red-600 group-hover:text-red-900" xmlns="http://www.w3.org/2000/svg"
                        fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    Delete
                </a>
            </div>
        </div>
    }

    <!-- Stats Cards -->
    <div class="mb-6 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        <!-- Total Care Professionals Card -->
        <div class="overflow-hidden rounded-lg bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-indigo-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="font-semibold text-gray-500">Total Care Professionals</h2>
                    <p class="text-2xl font-bold text-gray-800">@(Model.Pagination?.TotalCount ?? 0)</p>
                </div>
            </div>
        </div>

        <!-- Average Experience Card -->
        <div class="overflow-hidden rounded-lg bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-emerald-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-emerald-600" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="font-semibold text-gray-500">Avg. Experience</h2>
                    <p class="text-2xl font-bold text-gray-800">@(Model.CareProviders.Any() && Model.CareProviders.Any(p
                                                => p.YearsExperience > 0) ?
                                                Math.Round(Model.CareProviders.Average(e => e.YearsExperience), 1) : 0) years</p>
                </div>
            </div>
        </div>

        <!-- Total Providers Card -->
        <div class="overflow-hidden rounded-lg bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-amber-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-amber-600" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="font-semibold text-gray-500">Active Providers</h2>
                    <p class="text-2xl font-bold text-gray-800">@Model.CareProviders.Count</p>
                </div>
            </div>
        </div>

        <!-- Current Page Card -->
        <div class="overflow-hidden rounded-lg bg-white p-4 shadow-sm transition-all duration-300 hover:shadow-md">
            <div class="flex items-center">
                <div class="flex h-12 w-12 items-center justify-center rounded-lg bg-blue-100">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-600" fill="none"
                        viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H2a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                    </svg>
                </div>
                <div class="ml-4">
                    <h2 class="font-semibold text-gray-500">Page</h2>
                    <p class="text-2xl font-bold text-gray-800">@(Model.Pagination?.CurrentPage ?? 1) of
                        @(Model.Pagination?.TotalPages ?? 1)</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Care Professionals Table - Responsive Implementation -->
    <div class="rounded-lg bg-white shadow-sm">
        <!-- Responsive table container with horizontal scrolling for small screens -->
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <!-- Sticky header that remains visible when scrolling -->
                <thead class="bg-gray-50 sticky top-0 z-10">
                    <tr>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 whitespace-nowrap">
                            Care Professional
                        </th>
                        <!-- Hide Gender column on small screens -->
                        <th scope="col"
                            class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 whitespace-nowrap">
                            Gender
                        </th>
                        <!-- Hide Phone column on extra small screens -->
                        <th scope="col"
                            class="hidden md:table-cell px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 whitespace-nowrap">
                            Phone
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 whitespace-nowrap">
                            Experience
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 whitespace-nowrap">
                            Verification Status
                        </th>
                        <th scope="col"
                            class="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500 whitespace-nowrap">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-200 bg-white">
                    @if (Model.CareProviders.Count == 0)
                    {
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">
                                No care professionals found.
                            </td>
                        </tr>
                    }
                    else
                    {
                        @for (int i = 0; i < Model.CareProviders.Count; i++)
                        {
                            var provider = Model.CareProviders[i];
                            <!-- Responsive table row with data attributes for mobile view -->
                            <tr class="hover:bg-gray-50 group"
                                data-provider-id="@(provider.ProviderId ?? provider.UserId)"
                                data-user-id="@provider.UserId">
                                <!-- Care Professional Column - Always visible -->
                                <td class="px-6 py-4">
                                    <div class="flex items-center">
                                        <div class="flex h-10 w-10 flex-shrink-0 items-center justify-center rounded-full bg-indigo-100 text-indigo-700">
                                            @{
                                                var initials = string.IsNullOrEmpty(provider.Name) ? "CP" :
                                                string.Join("", provider.Name.Split(' ').Select(n => n.Length > 0 ? n[0].ToString() :
                                                "").Take(2));
                                            }
                                            <span class="text-sm font-medium">@initials</span>
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm font-medium text-gray-900">@(string.IsNullOrEmpty(provider.Name) ?
                                                                                    "No Name" : provider.Name)</div>
                                            @{
                                                // Generate a dummy ID based on the index in the loop
                                                var dummyId = $"CP{i + 1:D4}";
                                            }
                                            <div class="text-sm text-gray-500">ID: @dummyId</div>

                                            <!-- Mobile-only information (visible on xs screens) -->
                                            <div class="sm:hidden mt-2 space-y-1">
                                                <div class="text-xs text-gray-500">
                                                    <span class="font-medium">Gender:</span>
                                                    @(string.IsNullOrEmpty(provider.Gender) ? "Not specified" : provider.Gender)
                                                </div>
                                                <div class="text-xs text-gray-500">
                                                    <span class="font-medium">Phone:</span>
                                                    @(string.IsNullOrEmpty(provider.PhoneNumber) ? "Not provided" : provider.PhoneNumber)
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>

                                <!-- Gender Column - Hidden on xs screens -->
                                <td class="hidden sm:table-cell px-6 py-4">
                                    <div class="text-sm text-gray-900">@(string.IsNullOrEmpty(provider.Gender) ? "Not specified" :
                                                                    provider.Gender)</div>
                                </td>

                                <!-- Phone Column - Hidden on xs and sm screens -->
                                <td class="hidden md:table-cell px-6 py-4">
                                    <div class="text-sm text-gray-900">@(string.IsNullOrEmpty(provider.PhoneNumber) ? "Not provided"
                                                                    : provider.PhoneNumber)</div>
                                </td>

                                <!-- Experience Column - Always visible -->
                                <td class="px-6 py-4">
                                    <div class="text-sm text-gray-900">@provider.YearsExperience years</div>
                                    @if (provider.DateOfBirth.HasValue)
                                    {
                                        <div class="text-xs text-gray-500">DOB: @provider.DateOfBirth.Value.ToString("MMM dd, yyyy")</div>
                                    }
                                </td>

                                <!-- Verification Status Column - Always visible -->
                                <td class="px-6 py-4">
                                    <span class="inline-flex rounded-full px-2 text-xs font-semibold leading-5 
                                        @(provider.VerificationStatus == "Pending" 
                                            ? "bg-yellow-100 text-yellow-800" 
                                            : Model.GetVerificationStatusClass(provider.VerificationStatus))">
                                        @(string.IsNullOrEmpty(provider.VerificationStatus) ? "Unknown" : provider.VerificationStatus)
                                    </span>
                                    @if (provider.EmailVerified)
                                    {
                                        <div class="mt-1 text-xs text-gray-500">Email Verified</div>
                                    }
                                    else{
                                        <div class="mt-1 text-xs text-gray-500">Email Verification Pending</div>
                                    }
                                    @if (provider.LastLogin.HasValue)
                                    {
                                        <div class="mt-1 text-xs text-gray-500">Last login: @provider.LastLogin.Value.ToString("MMM dd, yyyy")</div>
                                    }
                                </td>

                                <!-- Actions Column - Always visible -->
                                <td class="px-6 py-4 text-right text-sm font-medium">
                                    <div class="relative inline-block text-left">
                                        <button type="button"
                                            class="inline-flex w-full justify-center gap-x-1.5 rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50"
                                            id="menu-button-@(i)" aria-expanded="false" aria-haspopup="true"
                                            onclick="toggleDropdown('@(i)')">
                                            Actions
                                            <svg class="-mr-1 h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor"
                                                aria-hidden="true">
                                                <path fill-rule="evenodd"
                                                    d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z"
                                                    clip-rule="evenodd" />
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    }
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modern Pagination -->
    @if (Model.Pagination != null)
    {
        <div class="mt-6 flex flex-col sm:flex-row items-center justify-between">
            <!-- Pagination Info -->
            <div class="text-sm text-gray-700 mb-4 sm:mb-0 text-center sm:text-left">
                Showing <span class="font-medium">@(((Model.Pagination.CurrentPage - 1) * Model.Pagination.PageSize) + 1)</span>
                to <span class="font-medium">@Math.Min(Model.Pagination.CurrentPage * Model.Pagination.PageSize, Model.Pagination.TotalCount)</span>
                of <span class="font-medium">@Model.Pagination.TotalCount</span> care professionals
            </div>

            <!-- Pagination Controls -->
            <nav class="flex justify-center sm:justify-end" aria-label="Pagination">
                <ul class="inline-flex items-center -space-x-px">
                    <!-- First Page -->
                    <li>
                        <a href="@Url.Action("CareProfessionals", new { page = 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })"
                           class="@(Model.Pagination.CurrentPage == 1 ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "bg-white text-gray-500 hover:bg-gray-50") inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-300 rounded-l-md"
                           aria-label="First page">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M15.79 14.77a.75.75 0 01-1.06.02L10 10.25l-4.73 4.52a.75.75 0 01-1.04-1.08l5.25-5a.75.75 0 011.04 0l5.25 5a.75.75 0 01.02 1.06z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>

                    <!-- Previous Page -->
                    <li>
                        <a href="@(Model.Pagination.HasPreviousPage ? Url.Action("CareProfessionals", new { page = Model.Pagination.CurrentPage - 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending }) : "#")"
                           class="@(Model.Pagination.HasPreviousPage ? "bg-white text-gray-500 hover:bg-gray-50" : "bg-gray-100 text-gray-400 cursor-not-allowed") inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-300"
                           aria-label="Previous page">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>

                    <!-- Page Numbers -->
                    @{
                        int startPage = Math.Max(1, Model.Pagination.CurrentPage - 2);
                        int endPage = Math.Min(Model.Pagination.TotalPages, startPage + 4);
                        if (endPage - startPage < 4)
                        {
                            startPage = Math.Max(1, endPage - 4);
                        }
                    }
                    @for (int i = startPage; i <= endPage; i++)
                    {
                        <li>
                            <a href="@Url.Action("CareProfessionals", new { page = i, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })"
                               class="@(Model.Pagination.CurrentPage == i ? "bg-indigo-600 text-white border-indigo-600" : "bg-white text-gray-500 hover:bg-gray-50 border-gray-300") inline-flex items-center px-4 py-2 text-sm font-medium border"
                               aria-label="Page @i">
                                @i
                            </a>
                        </li>
                    }

                    <!-- Next Page -->
                    <li>
                        <a href="@(Model.Pagination.HasNextPage ? Url.Action("CareProfessionals", new { page = Model.Pagination.CurrentPage + 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending }) : "#")"
                           class="@(Model.Pagination.HasNextPage ? "bg-white text-gray-500 hover:bg-gray-50" : "bg-gray-100 text-gray-400 cursor-not-allowed") inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-300"
                           aria-label="Next page">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>

                    <!-- Last Page -->
                    <li>
                        <a href="@Url.Action("CareProfessionals", new { page = Model.Pagination.TotalPages, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })"
                           class="@(Model.Pagination.CurrentPage == Model.Pagination.TotalPages ? "bg-gray-100 text-gray-400 cursor-not-allowed" : "bg-white text-gray-500 hover:bg-gray-50") inline-flex items-center px-3 py-2 text-sm font-medium border border-gray-300 rounded-r-md"
                           aria-label="Last page">
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M4.21 5.23a.75.75 0 011.06-.02L10 9.75l4.73-4.52a.75.75 0 011.04 1.08l-5.25 5a.75.75 0 01-1.04 0l-5.25-5a.75.75 0 01-.02-1.06z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </li>
                </ul>
            </nav>
        </div>
    }

    <!-- Approval Modal -->
    <div id="approvalModal" class="fixed inset-0 z-[9999] hidden overflow-hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <!-- Background overlay with click handler to close modal -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeApproveModal()"></div>

        <!-- Modal panel - centered both vertically and horizontally -->
        <div class="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-lg max-h-[90vh] overflow-y-auto rounded-lg bg-white shadow-xl transition-all">
            <form id="approvalForm">
                <input type="hidden" id="approvalProviderId" name="providerId" value="" />
                <div class="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-green-100 sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="h-6 w-6 text-green-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                            <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-title">
                                Approve Care Provider
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500">
                                    Are you sure you want to approve this care provider? This action will update their verification status to "Verified".
                                </p>
                                <div class="mt-4">
                                    <label for="approvalNotes" class="block text-sm font-medium text-gray-700">Additional Notes (Optional)</label>
                                    <textarea id="approvalNotes" name="notes" rows="3" class="mt-1 block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                    <button type="submit" class="inline-flex w-full justify-center rounded-md border border-transparent bg-green-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm">
                        Approve
                    </button>
                    <button type="button" onclick="closeApproveModal()" class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Rejection Modal -->
    <div id="rejectionModal" class="fixed inset-0 z-[9999] hidden overflow-hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
        <!-- Background overlay with click handler to close modal -->
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeRejectModal()"></div>

        <!-- Modal panel - centered both vertically and horizontally -->
        <div class="fixed top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-lg max-h-[90vh] overflow-y-auto rounded-lg bg-white shadow-xl transition-all">
            <form id="rejectionForm">
                <input type="hidden" id="rejectionProviderId" name="providerId" value="" />
                <div class="bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                            <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </div>
                        <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                            <h3 class="text-lg font-medium leading-6 text-gray-900" id="modal-title">
                                Reject Care Provider
                            </h3>
                            <div class="mt-2">
                                <p class="text-sm text-gray-500">
                                    Are you sure you want to reject this care provider? This action will update their verification status to "Rejected".
                                </p>
                                <div class="mt-4">
                                    <label for="rejectionReason" class="block text-sm font-medium text-gray-700">Rejection Reason <span class="text-red-500">*</span></label>
                                    <textarea id="rejectionReason" name="rejectionReason" rows="2" required class="mt-1 block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"></textarea>
                                    <p class="mt-1 text-xs text-gray-500">Please provide a reason for rejection. This will be visible to the provider.</p>
                                </div>
                                <div class="mt-4">
                                    <label for="rejectionNotes" class="block text-sm font-medium text-gray-700">Additional Notes (Optional)</label>
                                    <textarea id="rejectionNotes" name="notes" rows="2" class="mt-1 block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"></textarea>
                                    <p class="mt-1 text-xs text-gray-500">These notes are for internal use only.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
                    <button type="submit" class="inline-flex w-full justify-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-base font-medium text-white shadow-sm hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 sm:ml-3 sm:w-auto sm:text-sm">
                        Reject
                    </button>
                    <button type="button" onclick="closeRejectModal()" class="mt-3 inline-flex w-full justify-center rounded-md border border-gray-300 bg-white px-4 py-2 text-base font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 sm:mt-0 sm:w-auto sm:text-sm">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/care-professionals.js" asp-append-version="true"></script>
    <script>
        // Care Professionals page functionality
        document.addEventListener('DOMContentLoaded', function () {
            console.log('Care Professionals page loaded successfully');

            // Close dropdowns when clicking outside
            document.addEventListener('click', function (event) {
                const dropdowns = document.querySelectorAll('[id^="dropdown-"]');
                dropdowns.forEach(dropdown => {
                    // Check if the dropdown itself or its button was clicked
                    const button = document.querySelector(`[aria-labelledby="menu-button-${dropdown.id.split('-')[1]}"]`); // More robust selector for button if ID changes
                    const menuButton = document.getElementById(`menu-button-${dropdown.id.replace('dropdown-', '')}`);

                    let clickedOnButton = false;
                    if (menuButton && menuButton.contains(event.target)) {
                        clickedOnButton = true;
                    }
                    
                    if (!clickedOnButton && !dropdown.contains(event.target)) {
                        dropdown.classList.add('hidden');
                        if (menuButton) {
                             menuButton.setAttribute('aria-expanded', 'false');
                        }
                    }
                });
            });
        });

        // REVISED Toggle dropdown function with dynamic positioning
        function toggleDropdown(providerIndex) {
            const dropdown = document.getElementById(`dropdown-${providerIndex}`);
            const button = document.getElementById(`menu-button-${providerIndex}`);

            if (!dropdown || !button) {
                console.error('Dropdown or button not found for index:', providerIndex);
                return;
            }

            // Close all other dropdowns first
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => {
                if (dd.id !== `dropdown-${providerIndex}`) {
                    dd.classList.add('hidden');
                    const otherButton = document.getElementById(`menu-button-${dd.id.replace('dropdown-', '')}`);
                    if (otherButton) {
                        otherButton.setAttribute('aria-expanded', 'false');
                    }
                }
            });

            if (dropdown.classList.contains('hidden')) {
                // Temporarily make it visible (but not to user) to measure its dimensions
                dropdown.style.visibility = 'hidden';
                dropdown.style.display = 'block'; // Or 'flex' etc. depending on its layout, 'block' is usually fine for offsetWidth/Height

                const dropdownWidth = dropdown.offsetWidth;
                const dropdownHeight = dropdown.offsetHeight;

                // Restore its state before actual positioning and display
                dropdown.style.visibility = ''; // Revert to stylesheet (e.g., visible)
                dropdown.style.display = '';    // Revert to stylesheet (e.g., display from 'fixed' class, then 'hidden' will re-hide it)
                                                // The 'hidden' class is still on, so it will revert to display:none here.

                const buttonRect = button.getBoundingClientRect();
                const viewportWidth = window.innerWidth;
                const viewportHeight = window.innerHeight;
                const gap = 5; // Small gap from button and viewport edges

                let finalTop, finalLeft;

                // --- Vertical Positioning ---
                // Prefer below button
                if (buttonRect.bottom + dropdownHeight + gap <= viewportHeight) {
                    finalTop = buttonRect.bottom + gap;
                }
                // Else, try above button
                else if (buttonRect.top - dropdownHeight - gap >= 0) {
                    finalTop = buttonRect.top - dropdownHeight - gap;
                }
                // Else, it doesn't fit nicely above or below.
                // Position it relative to the button, trying to keep it in view.
                // Fallback: align top with button top, then ensure it's within viewport bounds.
                else {
                     // Try to align with the top of the button if it's too close to bottom
                    finalTop = buttonRect.top;
                }
                // Ensure finalTop is within viewport bounds as much as possible
                finalTop = Math.max(gap, Math.min(finalTop, viewportHeight - dropdownHeight - gap));


                // --- Horizontal Positioning (Attempt to align right edge of dropdown with right edge of button by default) ---
                finalLeft = buttonRect.right - dropdownWidth;

                // If aligning right-to-right makes it overflow to the left of viewport,
                // try aligning dropdown's left with button's left.
                if (finalLeft < gap) {
                    finalLeft = buttonRect.left;
                }

                // After choosing an initial alignment, ensure it doesn't overflow viewport edges.
                // Check right overflow
                if (finalLeft + dropdownWidth + gap > viewportWidth) {
                    finalLeft = viewportWidth - dropdownWidth - gap;
                }
                // Check left overflow (again, in case viewport is narrower than dropdown or initial position was bad)
                if (finalLeft < gap) {
                    finalLeft = gap;
                }
                
                // Apply the calculated position
                // `position: fixed` and `z-index` are handled by existing classes/styles on the dropdown div.
                dropdown.style.top = `${finalTop}px`;
                dropdown.style.left = `${finalLeft}px`;

                dropdown.classList.remove('hidden'); // Show the dropdown
                button.setAttribute('aria-expanded', 'true');
            } else {
                dropdown.classList.add('hidden');
                button.setAttribute('aria-expanded', 'false');
            }
        }

        // View care professional details function
        function viewDetails(providerId) {
            console.log('Viewing details for care professional:', providerId);
            // Close any open dropdowns
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => dd.classList.add('hidden'));

            // Add your view details logic here
            alert('View details functionality to be implemented');
        }

        // Edit care professional function
        function editProvider(providerId) {
            console.log('Editing care professional:', providerId);
            // Close any open dropdowns
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => dd.classList.add('hidden'));

            // Add your edit logic here
            alert('Edit functionality to be implemented');
        }

        // Delete care professional function
        function deleteProvider(providerId) {
            console.log('Deleting care professional:', providerId);
            // Close any open dropdowns
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => dd.classList.add('hidden'));

            if (confirm('Are you sure you want to delete this care professional?')) {
                // Add your delete logic here
                alert('Delete functionality to be implemented');
            }
        }

        // Approval Modal Functions
        function disableBodyScroll() {
            document.body.style.overflow = 'hidden';
            document.body.style.paddingRight = '15px'; // Prevent layout shift
        }

        function enableBodyScroll() {
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }

        function openApproveModal(providerId) {
            console.log('Opening approval modal for provider:', providerId);
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => dd.classList.add('hidden'));

            const table = document.querySelector('table');
            if (!table) {
                console.error('Table not found');
                alert('Error: Table not found.');
                return;
            }

            // More robustly select data rows from tbody
            const dataRows = Array.from(table.querySelectorAll('tbody tr'));
            const noProvidersRow = table.querySelector('tbody tr td[colspan="5"]'); // Check within tbody

            let providerRow;
            if (noProvidersRow) {
                console.log('No providers row found, cannot proceed');
                alert('No care providers found.');
                return;
            } else {
                const rowIndex = parseInt(providerId);
                if (rowIndex >= 0 && rowIndex < dataRows.length) {
                    providerRow = dataRows[rowIndex];
                } else {
                    console.error(`Row index ${rowIndex} is out of bounds (0-${dataRows.length-1})`);
                    alert(`Error: Row index ${rowIndex} is out of bounds.`);
                    return;
                }
            }

            if (!providerRow) {
                console.error(`Provider row not found for index: ${providerId}`);
                alert('Error: Provider information not found.');
                return;
            }

            const dataProviderId = providerRow.getAttribute('data-provider-id');
            const dataUserId = providerRow.getAttribute('data-user-id');
            const userId = dataUserId || dataProviderId;

            if (!userId) {
                console.error(`User ID not found in row: ${providerId}`);
                alert('Error: User ID not found.');
                return;
            }

            fetch(`/Dashboard/GetProviderApprovalRequests?userId=${encodeURIComponent(userId)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.requests && data.requests.length > 0) {
                        let selectedRequest = data.requests.find(req => req.status === 'Pending') ||
                                             data.requests.find(req => req.status === 'Rejected') || // Prioritize actionable states
                                             data.requests[0]; // Fallback to any request

                        const requestId = selectedRequest.id;
                        const requestStatus = selectedRequest.status;

                        document.getElementById('approvalProviderId').value = requestId;

                        const modalTitle = document.querySelector('#approvalModal #modal-title');
                        const existingStatusNote = document.querySelector('#approvalModal .status-note');
                        if (existingStatusNote) existingStatusNote.remove(); // Clear previous notes

                        let noteText = '';
                        if (requestStatus === 'Pending') {
                            modalTitle.textContent = `Approve Care Provider (Request #${requestId.substring(0, 8)})`;
                        } else if (requestStatus === 'Approved') { // Changed from "Verified" to "Approved" for consistency
                            modalTitle.textContent = `Re-approve Care Provider (Request #${requestId.substring(0, 8)})`;
                            noteText = 'Note: This provider is already approved.';
                        } else if (requestStatus === 'Rejected') {
                            modalTitle.textContent = `Approve Previously Rejected Provider (Request #${requestId.substring(0, 8)})`;
                            noteText = 'Note: This provider was previously rejected.';
                        }

                        if (noteText) {
                            const statusNoteEl = document.createElement('p');
                            statusNoteEl.className = 'status-note text-sm text-orange-500 mt-2';
                            statusNoteEl.textContent = noteText;
                            document.querySelector('#approvalModal .mt-2 p.text-gray-500').insertAdjacentElement('afterend', statusNoteEl);
                        }
                        
                        disableBodyScroll();
                        document.getElementById('approvalModal').classList.remove('hidden');
                    } else {
                        alert(data.message || 'No approval requests found for this provider.');
                    }
                })
                .catch(error => {
                    console.error('Error fetching approval requests:', error);
                    alert('An error occurred while fetching approval requests. Please try again.');
                });
        }

        function closeApproveModal() {
            document.getElementById('approvalModal').classList.add('hidden');
            enableBodyScroll();
            document.getElementById('approvalForm').reset();
            const modalTitle = document.querySelector('#approvalModal #modal-title');
            if (modalTitle) {
                modalTitle.textContent = 'Approve Care Provider';
            }
            const statusNote = document.querySelector('#approvalModal .status-note');
            if (statusNote) {
                statusNote.remove();
            }
        }

        function openRejectModal(providerId) {
            console.log('Opening rejection modal for provider:', providerId);
            const allDropdowns = document.querySelectorAll('[id^="dropdown-"]');
            allDropdowns.forEach(dd => dd.classList.add('hidden'));

            const table = document.querySelector('table');
            if (!table) {
                console.error('Table not found');
                alert('Error: Table not found.');
                return;
            }
            const dataRows = Array.from(table.querySelectorAll('tbody tr'));
            const noProvidersRow = table.querySelector('tbody tr td[colspan="5"]');

            let providerRow;
            if (noProvidersRow) {
                alert('No care providers found.');
                return;
            } else {
                const rowIndex = parseInt(providerId);
                if (rowIndex >= 0 && rowIndex < dataRows.length) {
                    providerRow = dataRows[rowIndex];
                } else {
                    alert(`Error: Row index ${rowIndex} is out of bounds.`);
                    return;
                }
            }

            if (!providerRow) {
                alert('Error: Provider information not found.');
                return;
            }

            const dataProviderId = providerRow.getAttribute('data-provider-id');
            const dataUserId = providerRow.getAttribute('data-user-id');
            const userId = dataUserId || dataProviderId;

            if (!userId) {
                alert('Error: User ID not found.');
                return;
            }

            fetch(`/Dashboard/GetProviderApprovalRequests?userId=${encodeURIComponent(userId)}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success && data.requests && data.requests.length > 0) {
                        let selectedRequest = data.requests.find(req => req.status === 'Pending') ||
                                             data.requests.find(req => req.status === 'Approved') || // Prioritize actionable
                                             data.requests[0];

                        const requestId = selectedRequest.id;
                        const requestStatus = selectedRequest.status;

                        document.getElementById('rejectionProviderId').value = requestId;

                        const modalTitle = document.querySelector('#rejectionModal #modal-title');
                        const existingStatusNote = document.querySelector('#rejectionModal .status-note');
                        if (existingStatusNote) existingStatusNote.remove();

                        let noteText = '';
                        if (requestStatus === 'Pending') {
                            modalTitle.textContent = `Reject Care Provider (Request #${requestId.substring(0, 8)})`;
                        } else if (requestStatus === 'Approved') {
                            modalTitle.textContent = `Reject Previously Approved Provider (Request #${requestId.substring(0, 8)})`;
                            noteText = 'Note: This provider is currently approved. Rejecting will override the approval.';
                        } else if (requestStatus === 'Rejected') {
                            modalTitle.textContent = `Re-reject Care Provider (Request #${requestId.substring(0, 8)})`;
                            noteText = 'Note: This provider is already rejected.';
                        }
                        
                        if (noteText) {
                            const statusNoteEl = document.createElement('p');
                            statusNoteEl.className = 'status-note text-sm text-orange-500 mt-2';
                            statusNoteEl.textContent = noteText;
                             document.querySelector('#rejectionModal .mt-2 p.text-gray-500').insertAdjacentElement('afterend', statusNoteEl);
                        }

                        disableBodyScroll();
                        document.getElementById('rejectionModal').classList.remove('hidden');
                    } else {
                        alert(data.message || 'No approval requests found for this provider.');
                    }
                })
                .catch(error => {
                    console.error('Error fetching approval requests:', error);
                    alert('An error occurred while fetching approval requests.');
                });
        }

        function closeRejectModal() {
            document.getElementById('rejectionModal').classList.add('hidden');
            enableBodyScroll();
            document.getElementById('rejectionForm').reset();
            const modalTitle = document.querySelector('#rejectionModal #modal-title');
            if (modalTitle) {
                modalTitle.textContent = 'Reject Care Provider';
            }
            const statusNote = document.querySelector('#rejectionModal .status-note');
            if (statusNote) {
                statusNote.remove();
            }
        }

        // Form submission handlers
        document.addEventListener('DOMContentLoaded', function() {
            // Approval form submission
            document.getElementById('approvalForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const providerId = document.getElementById('approvalProviderId').value; // This is the Request ID
                const notes = document.getElementById('approvalNotes').value;

                const submitButton = this.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;
                submitButton.disabled = true;
                submitButton.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Processing...';
                
                // CORRECTED URL: ¬ replaced with &
                const approveUrl = `/Dashboard/ApproveProvider?requestId=${encodeURIComponent(providerId)}¬es=${encodeURIComponent(notes || '')}&page=@(Model.Pagination?.CurrentPage ?? 1)&searchTerm=@(Model.SearchString ?? "")&sortBy=@(Model.SortBy ?? "createdAt")&sortDescending=@(Model.SortDescending.ToString().ToLower())`;

                fetch(approveUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json', // Not strictly needed for POST with query params, but good practice
                        'X-Requested-With': 'XMLHttpRequest'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    closeApproveModal();
                    // CORRECTED URL: ¬ replaced with &
                    window.location.href = `@Url.Action("CareProfessionals", new { page = Model.Pagination?.CurrentPage ?? 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })¬ificationType=${data.success ? 'success' : 'error'}¬ificationMessage=${encodeURIComponent(data.message || (data.success ? 'Provider has been approved successfully.' : 'An error occurred.'))}`;
                })
                .catch(error => {
                    console.error('Error approving provider:', error);
                    closeApproveModal();
                     // CORRECTED URL: ¬ replaced with &
                    window.location.href = `@Url.Action("CareProfessionals", new { page = Model.Pagination?.CurrentPage ?? 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })¬ificationType=error¬ificationMessage=${encodeURIComponent('An error occurred. Please try again.')}`;
                })
                .finally(() => {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText; // Use innerHTML if originalText contained SVG or complex content, else textContent
                });
            });

            // Rejection form submission
            document.getElementById('rejectionForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const providerId = document.getElementById('rejectionProviderId').value; // This is the Request ID
                const rejectionReason = document.getElementById('rejectionReason').value;
                const notes = document.getElementById('rejectionNotes').value;

                if (!rejectionReason.trim()) {
                    alert('Please provide a rejection reason.');
                    document.getElementById('rejectionReason').focus();
                    return;
                }

                const submitButton = this.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;
                submitButton.disabled = true;
                submitButton.innerHTML = '<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> Processing...';

                fetch(`/Dashboard/RejectProvider`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify({
                        requestId: providerId, // Ensure backend expects 'requestId' if that's what providerId means here
                        rejectionReason: rejectionReason,
                        notes: notes,
                        // Including these for consistency, though backend might not use them from body for redirect
                        page: @(Model.Pagination?.CurrentPage ?? 1),
                        searchTerm: '@(Model.SearchString ?? "")',
                        sortBy: '@(Model.SortBy ?? "createdAt")',
                        sortDescending: @(Model.SortDescending.ToString().ToLower())
                    })
                })
                .then(response => response.json())
                .then(data => {
                    closeRejectModal();
                     // CORRECTED URL: ¬ replaced with &
                    window.location.href = `@Url.Action("CareProfessionals", new { page = Model.Pagination?.CurrentPage ?? 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })¬ificationType=${data.success ? 'success' : 'error'}¬ificationMessage=${encodeURIComponent(data.message || (data.success ? 'Provider has been rejected successfully.' : 'An error occurred.'))}`;
                })
                .catch(error => {
                    console.error('Error rejecting provider:', error);
                    closeRejectModal();
                    // CORRECTED URL: ¬ replaced with &
                    window.location.href = `@Url.Action("CareProfessionals", new { page = Model.Pagination?.CurrentPage ?? 1, searchTerm = Model.SearchString, sortBy = Model.SortBy, sortDescending = Model.SortDescending })¬ificationType=error¬ificationMessage=${encodeURIComponent('An error occurred. Please try again.')}`;
                })
                 .finally(() => {
                    submitButton.disabled = false;
                    submitButton.innerHTML = originalText;
                });
            });
        });
    </script>
}