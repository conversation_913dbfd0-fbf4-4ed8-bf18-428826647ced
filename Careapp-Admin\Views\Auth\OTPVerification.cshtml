@model Northwind.ViewModels.OTPVerificationVm
@{
    ViewData["Title"] = "OTP Verification";
    Layout = "Layouts/_LayoutAuth";
}

<partial name="_SuccessNotification" />
<partial name="_ErrorNotification" />

<div class="font-[sans-serif]">
    <div class="flex flex-col items-center justify-center min-h-screen px-4">
        <div class="w-full max-w-md">
            <a href="@Url.Action("Index", "Dashboard")">
                <img src="~/images/logo-new.svg" alt="logo" class="mx-auto mb-6 block w-40"
                    style="filter: brightness(0) invert(0.1) sepia(1) saturate(5) hue-rotate(200deg);" />
            </a>

            <div class="rounded-2xl bg-white p-8 shadow">
                <h2 class="text-center text-2xl font-bold" style="color: #171e54;">Verify OTP Code</h2>
                <p class="text-center text-sm text-gray-600 mt-2">Enter the 6-digit code sent to your email</p>

                <form asp-controller="Auth" asp-action="OTPVerification" method="post" class="mt-8 space-y-6">
                    <input type="hidden" asp-for="Email" value="@Model.Email" />

                    <div>
                        <label for="otpCode" class="block text-sm font-medium text-gray-700">OTP Code</label>
                        <div class="mt-1">
                            <input asp-for="OTPCode" type="text"
                                class="w-full rounded-md border border-gray-300 px-4 py-3 text-sm text-gray-800"
                                style="outline-color: #171e54;" placeholder="Enter 6-digit code" maxlength="6" />
                            <span asp-validation-for="OTPCode" class="text-xs text-red-500"></span>
                        </div>
                    </div>

                    <div>
                        <button type="submit"
                            class="w-full rounded-lg px-4 py-3 text-sm tracking-wide text-white focus:outline-none"
                            style="background-color: #171e54; transition: background-color 0.3s ease;"
                            onmouseover="this.style.backgroundColor='#0f1436'"
                            onmouseout="this.style.backgroundColor='#171e54'">
                            Verify Code
                        </button>
                    </div>

                    <div class="text-center space-y-3">
                        <p class="text-sm text-gray-600">
                            Didn't receive the code?
                            <a href="@Url.Action("ForgotPassword")" class="font-medium hover:underline"
                                style="color: #171e54;">
                                Resend Code
                            </a>
                        </p>
                        <a href="@Url.Action("Login")" class="text-sm font-medium hover:underline block"
                            style="color: #171e54;">
                            Back to Login
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script>
        // Auto-focus on OTP input
        document.addEventListener('DOMContentLoaded', function () {
            document.getElementById('OTPCode').focus();
        });

        // Only allow numbers in OTP field
        document.getElementById('OTPCode').addEventListener('input', function (e) {
            this.value = this.value.replace(/[^0-9]/g, '');
        });
    </script>
}
