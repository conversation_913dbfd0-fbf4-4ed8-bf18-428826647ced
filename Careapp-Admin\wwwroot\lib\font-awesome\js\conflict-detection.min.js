/*!
 * Font Awesome Free 6.6.0 by @fontawesome - https://fontawesome.com
 * License - https://fontawesome.com/license/free (Icons: CC BY 4.0, Fonts: SIL OFL 1.1, Code: MIT License)
 * Copyright 2024 Fonticons, Inc.
 */
var t;t=function(){"use strict";let t={},e={};try{"undefined"!=typeof window&&(t=window),"undefined"!=typeof document&&(e=document)}catch(t){}const{userAgent:n=""}=t.navigator||{},f=t,l=e,o=!!f.document,a=!!l.documentElement&&!!l.head&&"function"==typeof l.addEventListener&&"function"==typeof l.createElement;~n.indexOf("MSIE")||n.indexOf("Trident/");function i(){l.removeEventListener("DOMContentLoaded",i),s=1,r.map(t=>t())}const r=[];let s=!1;function d(t){a&&(s?setTimeout(t,0):r.push(t))}a&&(s=(l.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(l.readyState),s||l.addEventListener("DOMContentLoaded",i));var p="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};var c=(function(t){function d(t,e){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}function s(t,e,n,o,a,i){return d((i=d(d(e,t),d(o,i)))<<a|i>>>32-a,n)}function u(t,e,n,o,a,i,r){return s(e&n|~e&o,t,e,a,i,r)}function h(t,e,n,o,a,i,r){return s(e&o|n&~o,t,e,a,i,r)}function g(t,e,n,o,a,i,r){return s(e^n^o,t,e,a,i,r)}function m(t,e,n,o,a,i,r){return s(n^(e|~o),t,e,a,i,r)}function r(t,e){var n,o,a,i;t[e>>5]|=128<<e%32,t[14+(e+64>>>9<<4)]=e;for(var r=1732584193,s=-271733879,c=-1732584194,f=271733878,l=0;l<t.length;l+=16)r=u(n=r,o=s,a=c,i=f,t[l],7,-680876936),f=u(f,r,s,c,t[l+1],12,-389564586),c=u(c,f,r,s,t[l+2],17,606105819),s=u(s,c,f,r,t[l+3],22,-1044525330),r=u(r,s,c,f,t[l+4],7,-176418897),f=u(f,r,s,c,t[l+5],12,1200080426),c=u(c,f,r,s,t[l+6],17,-1473231341),s=u(s,c,f,r,t[l+7],22,-45705983),r=u(r,s,c,f,t[l+8],7,1770035416),f=u(f,r,s,c,t[l+9],12,-1958414417),c=u(c,f,r,s,t[l+10],17,-42063),s=u(s,c,f,r,t[l+11],22,-1990404162),r=u(r,s,c,f,t[l+12],7,1804603682),f=u(f,r,s,c,t[l+13],12,-40341101),c=u(c,f,r,s,t[l+14],17,-1502002290),r=h(r,s=u(s,c,f,r,t[l+15],22,1236535329),c,f,t[l+1],5,-165796510),f=h(f,r,s,c,t[l+6],9,-1069501632),c=h(c,f,r,s,t[l+11],14,643717713),s=h(s,c,f,r,t[l],20,-373897302),r=h(r,s,c,f,t[l+5],5,-701558691),f=h(f,r,s,c,t[l+10],9,38016083),c=h(c,f,r,s,t[l+15],14,-660478335),s=h(s,c,f,r,t[l+4],20,-405537848),r=h(r,s,c,f,t[l+9],5,568446438),f=h(f,r,s,c,t[l+14],9,-1019803690),c=h(c,f,r,s,t[l+3],14,-187363961),s=h(s,c,f,r,t[l+8],20,1163531501),r=h(r,s,c,f,t[l+13],5,-1444681467),f=h(f,r,s,c,t[l+2],9,-51403784),c=h(c,f,r,s,t[l+7],14,1735328473),r=g(r,s=h(s,c,f,r,t[l+12],20,-1926607734),c,f,t[l+5],4,-378558),f=g(f,r,s,c,t[l+8],11,-2022574463),c=g(c,f,r,s,t[l+11],16,1839030562),s=g(s,c,f,r,t[l+14],23,-35309556),r=g(r,s,c,f,t[l+1],4,-1530992060),f=g(f,r,s,c,t[l+4],11,1272893353),c=g(c,f,r,s,t[l+7],16,-155497632),s=g(s,c,f,r,t[l+10],23,-1094730640),r=g(r,s,c,f,t[l+13],4,681279174),f=g(f,r,s,c,t[l],11,-358537222),c=g(c,f,r,s,t[l+3],16,-722521979),s=g(s,c,f,r,t[l+6],23,76029189),r=g(r,s,c,f,t[l+9],4,-640364487),f=g(f,r,s,c,t[l+12],11,-421815835),c=g(c,f,r,s,t[l+15],16,530742520),r=m(r,s=g(s,c,f,r,t[l+2],23,-995338651),c,f,t[l],6,-198630844),f=m(f,r,s,c,t[l+7],10,1126891415),c=m(c,f,r,s,t[l+14],15,-1416354905),s=m(s,c,f,r,t[l+5],21,-57434055),r=m(r,s,c,f,t[l+12],6,1700485571),f=m(f,r,s,c,t[l+3],10,-1894986606),c=m(c,f,r,s,t[l+10],15,-1051523),s=m(s,c,f,r,t[l+1],21,-2054922799),r=m(r,s,c,f,t[l+8],6,1873313359),f=m(f,r,s,c,t[l+15],10,-30611744),c=m(c,f,r,s,t[l+6],15,-1560198380),s=m(s,c,f,r,t[l+13],21,1309151649),r=m(r,s,c,f,t[l+4],6,-145523070),f=m(f,r,s,c,t[l+11],10,-1120210379),c=m(c,f,r,s,t[l+2],15,718787259),s=m(s,c,f,r,t[l+9],21,-343485551),r=d(r,n),s=d(s,o),c=d(c,a),f=d(f,i);return[r,s,c,f]}function c(t){for(var e="",n=32*t.length,o=0;o<n;o+=8)e+=String.fromCharCode(t[o>>5]>>>o%32&255);return e}function f(t){var e=[];for(e[(t.length>>2)-1]=void 0,o=0;o<e.length;o+=1)e[o]=0;for(var n=8*t.length,o=0;o<n;o+=8)e[o>>5]|=(255&t.charCodeAt(o/8))<<o%32;return e}function o(t){for(var e,n="0123456789abcdef",o="",a=0;a<t.length;a+=1)e=t.charCodeAt(a),o+=n.charAt(e>>>4&15)+n.charAt(15&e);return o}function n(t){return unescape(encodeURIComponent(t))}function a(t){return c(r(f(t=n(t)),8*t.length))}function i(t,e){return function(t,e){var n,o=f(t),a=[],i=[];for(a[15]=i[15]=void 0,16<o.length&&(o=r(o,8*t.length)),n=0;n<16;n+=1)a[n]=909522486^o[n],i[n]=1549556828^o[n];return e=r(a.concat(f(e)),512+8*e.length),c(r(i.concat(e),640))}(n(t),n(e))}function e(t,e,n){return e?n?i(e,t):o(i(e,t)):n?a(t):o(a(t))}var l;l=p,t.exports?t.exports=e:l.md5=e}(N={exports:{}}),N.exports);function u(t){if(null!==t&&"object"==typeof t)return t.src?c(t.src):t.href?c(t.href):t.innerText&&""!==t.innerText?c(t.innerText):void 0}const h="fa-kits-diag",g="fa-kits-node-under-test",m="data-md5",w="data-fa-detection-ignore",b="data-fa-detection-timeout",y="data-fa-detection-results-collection-max-wait",v=t=>{t.preventDefault(),t.stopPropagation()};function A(t){let{fn:i=()=>!0,initialDuration:e=1,maxDuration:r=f.FontAwesomeDetection.timeout,showProgress:s=!1,progressIndicator:c}=t;return new Promise(function(o,a){!function e(t,n){setTimeout(function(){var t=i();s&&console.info(c),t?o(t):(t=250+n)<=r?e(250,t):a("timeout")},t)}(e,0)})}function x(t){var{nodesTested:e,nodesFound:t}=t;f.FontAwesomeDetection=f.FontAwesomeDetection||{},f.FontAwesomeDetection.nodesTested=e,f.FontAwesomeDetection.nodesFound=t,f.FontAwesomeDetection.detectionDone=!0}function T(t){let e=0<arguments.length&&void 0!==t?t:()=>{};const n={conflict:{},noConflict:{}};f.onmessage=function(t){"file://"!==f.location.origin&&t.origin!==f.location.origin||t&&t.data&&("fontawesome-conflict"===t.data.type?n.conflict[t.data.md5]=t.data:"no-conflict"===t.data.type&&(n.noConflict[t.data.md5]=t.data))};var o=function(e){var n=Array.from(l.scripts).filter(t=>!t.hasAttribute(w)&&t!==e);const o={};for(let t=0;t<n.length;t++){const r=l.createElement("iframe");r.setAttribute("style","display:none;");const s=l.createElement("script");s.setAttribute("id",g);var a=u(n[t]);s.setAttribute(m,a),o[a]=n[t],""!==n[t].src&&(s.src=n[t].src),""!==n[t].innerText&&(s.innerText=n[t].innerText),s.async=!0;const c=l.createElement("script");c.setAttribute("id",h);var i="file://"===f.location.origin?"*":f.location.origin;c.innerText="(".concat(((n,o,a)=>{parent.FontAwesomeDetection.__pollUntil({fn:()=>!!window.FontAwesomeConfig||!!window.FontAwesomeKitConfig}).then(function(){var t=document.getElementById(n);parent.postMessage({type:"fontawesome-conflict",technology:"js",src:t.src,innerText:t.innerText,tagName:t.tagName,md5:o},a)}).catch(function(t){var e=document.getElementById(n);"timeout"===t?parent.postMessage({type:"no-conflict",src:e.src,innerText:e.innerText,tagName:e.tagName,md5:o},a):console.error(t)})}).toString(),")('").concat(g,"', '").concat(a,"', '").concat(i,"');"),r.onload=function(){r.contentWindow.addEventListener("error",v,!0),r.contentDocument.head.appendChild(c),r.contentDocument.head.appendChild(s)},d(()=>l.body.appendChild(r))}return o}(l.currentScript),t=function(){var e=Array.from(l.getElementsByTagName("link")).filter(t=>!t.hasAttribute(w)),n=Array.from(l.getElementsByTagName("style")).filter(t=>!t.hasAttribute(w)&&(!f.FontAwesomeConfig||!t.innerText.match(new RegExp("svg:not\\(:root\\)\\.".concat(f.FontAwesomeConfig.replacementClass)))));function o(t,e){const n=l.createElement("iframe");n.setAttribute("style","visibility: hidden; position: absolute; height: 0; width: 0;");var o="fa-test-icon-"+e;const a=l.createElement("i");a.setAttribute("class","fa fa-coffee"),a.setAttribute("id",o);const i=l.createElement("script");i.setAttribute("id",h);var r="file://"===f.location.origin?"*":f.location.origin;i.innerText="(".concat(((n,o,a,i)=>{parent.FontAwesomeDetection.__pollUntil({fn:()=>{var t=document.getElementById(o);const e=window.getComputedStyle(t),n=e.getPropertyValue("font-family");return!(!n.match(/FontAwesome/)&&!n.match(/Font Awesome [56]/))}}).then(()=>{var t=document.getElementById(n);parent.postMessage({type:"fontawesome-conflict",technology:"webfont",href:t.href,innerText:t.innerText,tagName:t.tagName,md5:a},i)}).catch(function(t){var e=document.getElementById(n);"timeout"===t?parent.postMessage({type:"no-conflict",technology:"webfont",href:e.src,innerText:e.innerText,tagName:e.tagName,md5:a},i):console.error(t)})}).toString(),")('").concat(g,"', '").concat(o||"foo","', '").concat(e,"', '").concat(r,"');"),n.onload=function(){n.contentWindow.addEventListener("error",v,!0),n.contentDocument.head.appendChild(i),n.contentDocument.head.appendChild(t),n.contentDocument.body.appendChild(a)},d(()=>l.body.appendChild(n))}const a={};for(let t=0;t<e.length;t++){const c=l.createElement("link");c.setAttribute("id",g),c.setAttribute("href",e[t].href),c.setAttribute("rel",e[t].rel);var i=u(e[t]);c.setAttribute(m,i),a[i]=e[t],o(c,i)}for(let t=0;t<n.length;t++){var r=l.createElement("style");r.setAttribute("id",g);var s=u(n[t]);r.setAttribute(m,s),r.innerText=n[t].innerText,a[s]=n[t],o(r,s)}return a}();const a={...o,...t},i=Object.keys(o).length+Object.keys(t).length;t=f.FontAwesomeDetection.timeout+f.FontAwesomeDetection.resultsCollectionMaxWait;console.group("Font Awesome Detector"),0===i?(console.info("%cAll Good!","color: green; font-size: large"),console.info("We didn't find anything that needs testing for conflicts. Ergo, no conflicts.")):(console.info("Testing ".concat(i," possible conflicts.")),console.info("We'll wait about ".concat(Math.round(f.FontAwesomeDetection.timeout/10)/100," seconds while testing these and\n")+"then up to another ".concat(Math.round(f.FontAwesomeDetection.resultsCollectionMaxWait/10)/100," to allow the browser time\n")+"to accumulate the results. But we'll probably be outta here way before then.\n\n"),console.info("You can adjust those durations by assigning values to these attributes on the <script> element that loads this detection:"),console.info("\t%c".concat(b,"%c: milliseconds to wait for each test before deciding whether it's a conflict."),"font-weight: bold;","font-size: normal;"),console.info("\t%c".concat(y,"%c: milliseconds to wait for the browser to accumulate test results before giving up."),"font-weight: bold;","font-size: normal;"),A({maxDuration:t,showProgress:!0,progressIndicator:"waiting...",fn:()=>Object.keys(n.conflict).length+Object.keys(n.noConflict).length>=i}).then(()=>{console.info("DONE!"),x({nodesTested:n,nodesFound:a}),e({nodesTested:n,nodesFound:a}),console.groupEnd()}).catch(t=>{"timeout"===t?console.info("TIME OUT! We waited until we got tired. Here's what we found:"):(console.info("Whoops! We hit an error:",t),console.info("Here's what we'd found up until that error:")),x({nodesTested:n,nodesFound:a}),e({nodesTested:n,nodesFound:a}),console.groupEnd()}))}var E=f.FontAwesomeDetection||{},k={...{report:function(t){var e,{nodesTested:n,nodesFound:o}=t;const a={};for(e in o)n.conflict[e]||n.noConflict[e]||(a[e]=o[e]);if(0<(t=Object.keys(n.conflict).length)){console.info("%cConflict".concat(1<t?"s":""," found:"),"color: darkred; font-size: large");const c={};for(var i in n.conflict){const f=n.conflict[i];c[i]={tagName:f.tagName,"src/href":f.src||f.href||"n/a","innerText excerpt":f.innerText&&""!==f.innerText?f.innerText.slice(0,200)+"...":"(empty)"}}console.table(c)}if(0<(t=Object.keys(n.noConflict).length)){console.info("%cNo conflict".concat(1<t?"s":""," found with ").concat(1===t?"this":"these",":"),"color: green; font-size: large");const l={};for(var r in n.noConflict){const d=n.noConflict[r];l[r]={tagName:d.tagName,"src/href":d.src||d.href||"n/a","innerText excerpt":d.innerText&&""!==d.innerText?d.innerText.slice(0,200)+"...":"(empty)"}}console.table(l)}if(0<(t=Object.keys(a).length)){console.info("%cLeftovers--we timed out before collecting test results for ".concat(1===t?"this":"these",":"),"color: blue; font-size: large");const u={};for(var s in a){const h=a[s];u[s]={tagName:h.tagName,"src/href":h.src||h.href||"n/a","innerText excerpt":h.innerText&&""!==h.innerText?h.innerText.slice(0,200)+"...":"(empty)"}}console.table(u)}},timeout:+(l.currentScript.getAttribute(b)||"2000"),resultsCollectionMaxWait:+(l.currentScript.getAttribute(y)||"5000")},...E,__pollUntil:A,md5ForNode:u,detectionDone:!1,nodesTested:null,nodesFound:null};f.FontAwesomeDetection=k;var C="classic",D={fak:"kit","fa-kit":"kit"},F={fakd:"kit-duotone","fa-kit-duotone":"kit-duotone"},N={classic:{fa:"solid",fas:"solid","fa-solid":"solid",far:"regular","fa-regular":"regular",fal:"light","fa-light":"light",fat:"thin","fa-thin":"thin",fad:"duotone","fa-duotone":"duotone",fab:"brands","fa-brands":"brands"},sharp:{fa:"solid",fass:"solid","fa-solid":"solid",fasr:"regular","fa-regular":"regular",fasl:"light","fa-light":"light",fast:"thin","fa-thin":"thin"},"sharp-duotone":{fa:"solid",fasds:"solid","fa-solid":"solid"}},E={kit:"fak"},k={"kit-duotone":"fakd"};const O=(()=>{try{return"production"===process.env.NODE_ENV}catch(t){return!1}})();function j(t){return new Proxy(t,{get(t,e){return e in t?t[e]:t[C]}})}const M={...N};M[C]={...N[C],...D,...F};j(M);const S={classic:{solid:"fas",regular:"far",light:"fal",thin:"fat",duotone:"fad",brands:"fab"},sharp:{solid:"fass",regular:"fasr",light:"fasl",thin:"fast"},"sharp-duotone":{solid:"fasds"}};S[C]={...S[C],...E,...k};k=j(S);const I={classic:{fab:"fa-brands",fad:"fa-duotone",fal:"fa-light",far:"fa-regular",fas:"fa-solid",fat:"fa-thin"},sharp:{fass:"fa-solid",fasr:"fa-regular",fasl:"fa-light",fast:"fa-thin"},"sharp-duotone":{fasds:"fa-solid"}};I[C]={...I[C],fak:"fa-kit"};j(I);const W={classic:{"fa-brands":"fab","fa-duotone":"fad","fa-light":"fal","fa-regular":"far","fa-solid":"fas","fa-thin":"fat"},sharp:{"fa-solid":"fass","fa-regular":"fasr","fa-light":"fasl","fa-thin":"fast"},"sharp-duotone":{"fa-solid":"fasds"}};W[C]={...W[C],"fa-kit":"fak"};j(W),j({classic:{900:"fas",400:"far",normal:"far",300:"fal",100:"fat"},sharp:{900:"fass",400:"fasr",300:"fasl",100:"fast"},"sharp-duotone":{900:"fasds"}});const B=new Set;Object.keys(k[C]).map(B.add.bind(B)),Object.keys(k.sharp).map(B.add.bind(B)),Object.keys(k["sharp-duotone"]).map(B.add.bind(B)),function(t){try{for(var e=arguments.length,n=new Array(1<e?e-1:0),o=1;o<e;o++)n[o-1]=arguments[o];t(...n)}catch(t){if(!O)throw t}}(()=>{o&&a&&T(window.FontAwesomeDetection.report)})},("object"!=typeof exports||"undefined"==typeof module)&&"function"==typeof define&&define.amd?define(t):t();