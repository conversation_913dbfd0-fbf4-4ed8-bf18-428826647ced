run:
	ASPNETCORE_ENVIRONMENT=Production dotnet run --project ./Careapp-Admin/Careapp-Admin.csproj

dev:
	ASPNETCORE_ENVIRONMENT=Development dotnet run --project ./Careapp-Admin/Careapp-Admin.csproj

build:
	dotnet build ./Careapp-Admin/Careapp-Admin.csproj

clean:
	dotnet clean ./Careapp-Admin/Careapp-Admin.csproj

rebuild:
	dotnet build --no-incremental ./Careapp-Admin/Careapp-Admin.csproj

publish:
	dotnet publish -c Release

run-publish:
	dotnet run --project ./Careapp-Admin/Careapp-Admin.csproj

migrate:
	dotnet ef migrations add $(migration_name)
	dotnet ef database update

migrate-down:
	dotnet ef migrations remove $(migration_name)
	dotnet ef database update