using System.IdentityModel.Tokens.Jwt;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Options;
using Northwind.Configuration;
using Northwind.Models.Auth;

namespace Northwind.Services.Auth
{
    public class JwtAuthService : IJwtAuthService
    {
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ApiSettings _apiSettings;
        private readonly JwtSettings _jwtSettings;

        public JwtAuthService(
            IHttpClientFactory httpClientFactory,
            IHttpContextAccessor httpContextAccessor,
            IOptions<ApiSettings> apiSettings,
            IOptions<JwtSettings> jwtSettings
        )
        {
            _httpClientFactory = httpClientFactory;
            _httpContextAccessor = httpContextAccessor;
            _apiSettings = apiSettings.Value;
            _jwtSettings = jwtSettings.Value;
        }

        public async Task<AuthResponse> LoginAsync(string email, string password)
        {
            var client = _httpClientFactory.CreateClient();
            client.BaseAddress = new Uri(_apiSettings.BaseUrl);

            var loginRequest = new LoginRequest { Email = email, Password = password };

            var content = new StringContent(
                JsonSerializer.Serialize(loginRequest),
                Encoding.UTF8,
                "application/json"
            );

            var response = await client.PostAsync(_apiSettings.AuthEndpoint, content);

            if (response.IsSuccessStatusCode)
            {
                var authResponse = await response.Content.ReadFromJsonAsync<AuthResponse>();

                if (authResponse != null && authResponse.Success)
                {
                    // Store tokens in cookies
                    StoreTokens(
                        authResponse.Payload.AccessToken,
                        authResponse.Payload.RefreshToken,
                        authResponse.Payload.ExpiresIn
                    );
                    return authResponse;
                }
            }

            return new AuthResponse
            {
                Success = false,
                Message = "Authentication failed",
                StatusCode = (int)response.StatusCode,
            };
        }

        public async Task<AuthResponse> RefreshTokenAsync(string refreshToken)
        {
            var client = _httpClientFactory.CreateClient();
            client.BaseAddress = new Uri(_apiSettings.BaseUrl);

            var refreshRequest = new RefreshTokenRequest { RefreshToken = refreshToken };

            var content = new StringContent(
                JsonSerializer.Serialize(refreshRequest),
                Encoding.UTF8,
                "application/json"
            );

            var response = await client.PostAsync(_apiSettings.RefreshTokenEndpoint, content);

            if (response.IsSuccessStatusCode)
            {
                var authResponse = await response.Content.ReadFromJsonAsync<AuthResponse>();

                if (authResponse != null && authResponse.Success)
                {
                    // Store new tokens in cookies
                    StoreTokens(
                        authResponse.Payload.AccessToken,
                        authResponse.Payload.RefreshToken,
                        authResponse.Payload.ExpiresIn
                    );
                    return authResponse;
                }
            }

            // If refresh fails, we'll return a failed response but not clear the tokens
            // as they might still be valid for some time
            return new AuthResponse
            {
                Success = false,
                Message = "Token refresh failed",
                StatusCode = (int)response.StatusCode,
            };
        }

        public async Task<bool> LogoutAsync()
        {
            // Get the current token
            var token = GetToken();
            if (string.IsNullOrEmpty(token))
            {
                // No token to logout, just clear cookies
                ClearTokens();
                return true;
            }

            try
            {
                var client = _httpClientFactory.CreateClient();
                client.BaseAddress = new Uri(_apiSettings.BaseUrl);

                var logoutRequest = new LogoutRequest { Token = token };

                var content = new StringContent(
                    JsonSerializer.Serialize(logoutRequest),
                    Encoding.UTF8,
                    "application/json"
                );

                var response = await client.PostAsync(_apiSettings.LogoutEndpoint, content);

                // Whether the API call succeeds or fails, we clear the local tokens
                ClearTokens();

                return response.IsSuccessStatusCode;
            }
            catch
            {
                // Even if the API call fails, we still clear the local tokens
                ClearTokens();
                return false;
            }
        }

        public bool IsAuthenticated()
        {
            return !string.IsNullOrEmpty(GetToken());
        }

        public string GetToken()
        {
            return _httpContextAccessor.HttpContext?.Request.Cookies[_jwtSettings.TokenCookieName];
        }

        public string GetRefreshToken()
        {
            return _httpContextAccessor
                .HttpContext
                ?.Request
                .Cookies[_jwtSettings.RefreshTokenCookieName];
        }

        public DateTime? GetTokenExpiryTime()
        {
            var token = GetToken();
            if (string.IsNullOrEmpty(token))
                return null;

            try
            {
                var handler = new JwtSecurityTokenHandler();
                var jwtToken = handler.ReadJwtToken(token);

                if (jwtToken.ValidTo == DateTime.MinValue)
                    return null;

                return jwtToken.ValidTo.ToUniversalTime();
            }
            catch
            {
                return null;
            }
        }

        public bool ShouldRefreshToken()
        {
            var expiryTime = GetTokenExpiryTime();
            if (!expiryTime.HasValue)
                return false;

            // Check if token will expire within the threshold time
            return expiryTime.Value.AddMinutes(-_apiSettings.RefreshTokenThresholdMinutes)
                < DateTime.UtcNow;
        }

        public void StoreTokens(string token, string refreshToken, DateTime expiresIn)
        {
            var cookieOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = true, // Set to true in production with HTTPS
                SameSite = SameSiteMode.Strict,
                Expires = expiresIn,
            };

            _httpContextAccessor.HttpContext?.Response.Cookies.Append(
                _jwtSettings.TokenCookieName,
                token,
                cookieOptions
            );

            var refreshTokenOptions = new CookieOptions
            {
                HttpOnly = true,
                Secure = true, // Set to true in production with HTTPS
                SameSite = SameSiteMode.Strict,
                Expires = DateTime.UtcNow.AddDays(30), // Refresh token typically has a longer expiry
            };

            _httpContextAccessor.HttpContext?.Response.Cookies.Append(
                _jwtSettings.RefreshTokenCookieName,
                refreshToken,
                refreshTokenOptions
            );
        }

        public void ClearTokens()
        {
            _httpContextAccessor.HttpContext?.Response.Cookies.Delete(_jwtSettings.TokenCookieName);
            _httpContextAccessor.HttpContext?.Response.Cookies.Delete(
                _jwtSettings.RefreshTokenCookieName
            );
        }
    }
}
